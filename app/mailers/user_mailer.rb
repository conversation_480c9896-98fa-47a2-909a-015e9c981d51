class UserMailer < ApplicationMailer
  def password_reset
    @user = params[:user]
    @signed_id = @user.generate_token_for(:password_reset)

    mail to: @user.email, subject: 'Reset your password'
  end

  def email_verification
    @user = params[:user]
    @signed_id = @user.generate_token_for(:email_verification)

    mail to: @user.email, subject: 'Verify your email'
  end

  def team_invitation
    @invited_user = params[:invited_user]
    @organization = params[:organization]
    @inviter = params[:inviter]
    @signed_id = @invited_user.generate_token_for(:password_reset)

    mail to: @invited_user.email,
         subject:
           "You've been invited to join #{@organization.name} on Ghostwrote"
  end
end
