class Identity::EmailVerificationsController < ApplicationController
  skip_before_action :authenticate, only: %i[show resend] # Allow resend without login
  skip_before_action :require_onboarding_completion

  before_action :set_user, only: :show

  def show
    @user.update! verified: true

    # Create a session for the verified user
    session_record = @user.sessions.create!
    cookies.signed.permanent[:session_token] = {
      value: session_record.id,
      httponly: true,
    }

    # Redirect to personal information step to start onboarding
    redirect_to onboarding_personal_path, notice: "Thank you for verifying your email address! Let's get you set up."
  end

  # Existing create action (likely triggered after sign up)
  def create
    send_email_verification(Current.user)
    redirect_to root_path, notice: "We sent a verification email to your email address"
  end

  # New action for resending from password reset page
  def resend
    @user = User.find_by(email: params[:email])

    if @user.nil?
      redirect_to new_identity_password_reset_path, alert: "We couldn't find an account with that email address."
    elsif @user.verified?
      redirect_to new_identity_password_reset_path, alert: "This email address has already been verified."
    elsif @user.verification_email_sent_at.present? && @user.verification_email_sent_at > 3.minutes.ago
      redirect_to new_identity_password_reset_path, alert: "Please wait a few minutes before requesting another verification email."
    else
      send_email_verification(@user)
      @user.update(verification_email_sent_at: Time.current)
      redirect_to new_identity_password_reset_path, notice: "We sent a new verification email to your email address."
    end
  end

  private
    def set_user
      @user = User.find_by_token_for!(:email_verification, params[:sid])
    rescue StandardError
      redirect_to new_identity_password_reset_path, alert: "That email verification link is invalid" # Redirect to password reset if link invalid
    end

    # Refactored to accept user argument
    def send_email_verification(user)
      UserMailer.with(user: user).email_verification.deliver_later
    end
end
