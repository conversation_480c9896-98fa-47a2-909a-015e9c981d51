class RegistrationsController < ApplicationController
  layout 'auth'
  skip_before_action :authenticate
  skip_before_action :require_onboarding_completion

  def new
    @user = User.new
  end

  def create
    @user = User.new(user_params.except(:user_type)) # Exclude user_type from direct mass assignment
    @user.signup_intent = params[:user_type] # Store the intent
    @user.onboarding_step = 'personal' # Start everyone at personal details
    @user.onboarding_completed = false

    if @user.save
      session_record = @user.sessions.create!
      cookies.signed.permanent[:session_token] = {
        value: session_record.id,
        httponly: true,
      }

      send_email_verification

      # Redirect to sign-in page with verification message
      redirect_to sign_in_path,
                  notice:
                    'Welcome! Please check your email and click the verification link to continue.'
    else
      render :new, status: :unprocessable_entity
    end
  end

  private

  def user_params
    # Permit user_type but handle it separately in create action
    params.permit(:email, :password, :password_confirmation, :user_type)
  end

  def send_email_verification
    UserMailer.with(user: @user).email_verification.deliver_later
  end
end
