module Scout
  class TalentOnboardingController < Scout::BaseController
    before_action :ensure_scout_completed
    before_action :redirect_if_talent_completed

    def new
      @profile = Current.user.build_talent_profile
      pre_populate_profile_from_scout_data
    end

    def create
      @profile = Current.user.build_talent_profile

      # Process the params before updating the profile
      processed_params = process_profile_params(profile_params)

      if @profile.update(processed_params)
        # Mark talent signup as complete upon successful profile save
        Current.user.update(talent_signup_completed: true)

        # Assign talent role to the user
        talent_role = Role.find_or_create_by(name: 'talent')
        unless Current.user.has_role?(:talent)
          UserRole.create!(user: Current.user, role: talent_role)
        end

        redirect_to(
          launchpad_path,
          notice: 'Welcome to the Talent area! You now have access to both Scout and Talent features. 🎉'
        )
      else
        render :new, status: :unprocessable_entity
      end
    end

    private

    def ensure_scout_completed
      unless Current.user&.scout_signup_completed?
        redirect_to launchpad_path,
                    alert: 'You need to complete Scout signup first before becoming a Talent.'
      end
    end

    def redirect_if_talent_completed
      if Current.user&.talent_signup_completed?
        redirect_to launchpad_path,
                    notice: 'You have already completed Talent onboarding.'
      end
    end

    def pre_populate_profile_from_scout_data
      user = Current.user

      # Pre-populate basic information that would be common between scout and talent profiles
      if user.time_zone.present?
        # Convert time zone to a more user-friendly location format
        # This is a basic mapping - could be enhanced with more sophisticated location detection
        @profile.location = map_timezone_to_location(user.time_zone)
      end

      # Set default availability to available for new talent profiles
      @profile.availability_status = 'available'

      # Set default pricing model to hourly
      @profile.pricing_model = 'hourly'

      # Create a basic headline using the user's name
      if user.first_name.present? && user.last_name.present?
        @profile.headline = "#{user.name.full} - Content Creator & Writer"
      end

      # Set default location preference based on time zone
      @profile.location_preference = map_timezone_to_location_preference(user.time_zone)
    end

    def map_timezone_to_location(time_zone)
      return nil unless time_zone.present?

      # Basic mapping of common time zones to locations
      case time_zone
      when /Eastern/i, /New_York/i
        "New York, NY"
      when /Central/i, /Chicago/i
        "Chicago, IL"
      when /Mountain/i, /Denver/i
        "Denver, CO"
      when /Pacific/i, /Los_Angeles/i
        "Los Angeles, CA"
      when /London/i, /GMT/i
        "London, UK"
      when /Paris/i, /Berlin/i, /Rome/i
        "Europe"
      when /Tokyo/i, /Asia/i
        "Asia"
      else
        # For other time zones, just use the time zone name as a fallback
        time_zone.gsub('_', ' ')
      end
    end

    def map_timezone_to_location_preference(time_zone)
      return 'north_america' unless time_zone.present?

      case time_zone
      when /America/i, /US/i, /Canada/i
        'north_america'
      when /Europe/i, /London/i, /Paris/i, /Berlin/i, /Rome/i
        'europe'
      when /Asia/i, /Tokyo/i, /Shanghai/i, /Mumbai/i
        'asia'
      when /Africa/i
        'africa'
      when /Australia/i, /Sydney/i
        'asia' # Closest option available
      else
        'north_america' # Default fallback
      end
    end

    def profile_params
      params
        .require(:talent_profile)
        .permit(
          :bio,
          :looking_for,
          :about,
          :vsl_link,
          :availability_status,
          :price_range_min,
          :price_range_max,
          :pricing_model,
          :portfolio_link,
          :linkedin_url,
          :x_url,
          :website_url,
          :platform_choice,
          :location,
          :instagram_url,
          :threads_url,
          :is_agency,
          :location_preference,
          :headline,
          :skills,
          :niches,
          skills: [],
          niches: [],
          outcomes: [],
          ghostwriter_type: [],
          social_media_specialty: [],
          achievement_badges: [],
        )
    end

    def process_profile_params(params)
      # Convert string skills/niches to arrays if needed
      params[:skills] = params[:skills].to_s.split(',') if params[:skills]
        .is_a?(String)
      params[:niches] = params[:niches].to_s.split(',') if params[:niches]
        .is_a?(String)
      params
    end
  end
end
