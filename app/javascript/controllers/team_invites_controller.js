import { Controller } from "@hotwired/stimulus";

// Team Invites Controller
// Handles team member email input, validation, and preview for onboarding
export default class extends Controller {
  static targets = ["emailInput", "preview", "emailList", "submitButton"];

  connect() {
    console.log("TeamInvites controller connected");
    
    // Add input event listener for real-time validation
    if (this.hasEmailInputTarget) {
      this.emailInputTarget.addEventListener('input', this.debounce(this.validateEmails.bind(this), 300));
      this.emailInputTarget.addEventListener('blur', this.validateEmails.bind(this));
    }
  }

  // Validate and preview email addresses
  validateEmails() {
    const emailText = this.emailInputTarget.value.trim();
    
    if (!emailText) {
      this.hidePreview();
      return;
    }

    // Parse email addresses
    const emails = this.parseEmails(emailText);
    const validEmails = [];
    const invalidEmails = [];

    emails.forEach(email => {
      if (this.isValidEmail(email)) {
        validEmails.push(email);
      } else {
        invalidEmails.push(email);
      }
    });

    // Update preview
    this.updatePreview(validEmails, invalidEmails);
    
    // Update submit button state
    this.updateSubmitButton(validEmails.length > 0 && invalidEmails.length === 0);
  }

  // Parse email addresses from text input
  parseEmails(text) {
    return text
      .split(/[,\n\r]+/) // Split by comma, newline, or carriage return
      .map(email => email.trim())
      .filter(email => email.length > 0);
  }

  // Validate email format
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Update email preview
  updatePreview(validEmails, invalidEmails) {
    if (validEmails.length === 0 && invalidEmails.length === 0) {
      this.hidePreview();
      return;
    }

    this.showPreview();
    
    let previewHTML = '';
    
    // Show valid emails
    validEmails.forEach(email => {
      previewHTML += `
        <div class="flex items-center justify-between p-2 bg-emerald-50 border border-emerald-200 rounded-lg">
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-emerald-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-sm text-emerald-800">${email}</span>
          </div>
          <button type="button" 
                  class="text-emerald-600 hover:text-emerald-800 transition-colors"
                  data-action="click->team-invites#removeEmail"
                  data-email="${email}">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
      `;
    });
    
    // Show invalid emails
    invalidEmails.forEach(email => {
      previewHTML += `
        <div class="flex items-center justify-between p-2 bg-red-50 border border-red-200 rounded-lg">
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-sm text-red-800">${email}</span>
            <span class="text-xs text-red-600">(invalid format)</span>
          </div>
          <button type="button" 
                  class="text-red-600 hover:text-red-800 transition-colors"
                  data-action="click->team-invites#removeEmail"
                  data-email="${email}">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
      `;
    });
    
    if (this.hasEmailListTarget) {
      this.emailListTarget.innerHTML = previewHTML;
    }
  }

  // Remove email from input
  removeEmail(event) {
    const emailToRemove = event.currentTarget.dataset.email;
    const currentEmails = this.parseEmails(this.emailInputTarget.value);
    const updatedEmails = currentEmails.filter(email => email !== emailToRemove);
    
    this.emailInputTarget.value = updatedEmails.join(', ');
    this.validateEmails();
  }

  // Show preview section
  showPreview() {
    if (this.hasPreviewTarget) {
      this.previewTarget.classList.remove('hidden');
    }
  }

  // Hide preview section
  hidePreview() {
    if (this.hasPreviewTarget) {
      this.previewTarget.classList.add('hidden');
    }
  }

  // Update submit button state
  updateSubmitButton(isValid) {
    if (!this.hasSubmitButtonTarget) return;
    
    if (isValid) {
      this.submitButtonTarget.disabled = false;
      this.submitButtonTarget.classList.remove('opacity-50', 'cursor-not-allowed');
      this.submitButtonTarget.classList.add('hover:bg-stone-800');
    } else {
      this.submitButtonTarget.disabled = true;
      this.submitButtonTarget.classList.add('opacity-50', 'cursor-not-allowed');
      this.submitButtonTarget.classList.remove('hover:bg-stone-800');
    }
  }

  // Debounce function to limit API calls
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  // Handle form submission
  submitForm(event) {
    const validEmails = this.parseEmails(this.emailInputTarget.value)
      .filter(email => this.isValidEmail(email));
    
    if (validEmails.length === 0) {
      event.preventDefault();
      this.showError('Please enter at least one valid email address');
      return false;
    }

    // Track analytics
    if (typeof posthog !== 'undefined') {
      posthog.capture('onboarding_team_invites_sent', {
        invite_count: validEmails.length,
        emails: validEmails
      });
    }

    return true;
  }

  // Show error message
  showError(message) {
    // Remove existing error
    const existingError = this.element.querySelector('.team-invites-error');
    if (existingError) {
      existingError.remove();
    }

    // Create error element
    const errorElement = document.createElement('div');
    errorElement.className = 'team-invites-error mt-2 p-3 bg-red-50 border border-red-200 rounded-lg text-sm text-red-700';
    errorElement.innerHTML = `
      <div class="flex items-center">
        <svg class="w-4 h-4 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
        </svg>
        ${message}
      </div>
    `;

    // Insert after email input
    this.emailInputTarget.parentNode.insertBefore(errorElement, this.emailInputTarget.nextSibling);

    // Auto-hide after 5 seconds
    setTimeout(() => {
      if (errorElement && errorElement.parentNode) {
        errorElement.remove();
      }
    }, 5000);
  }
}
