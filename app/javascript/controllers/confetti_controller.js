import { Controller } from "@hotwired/stimulus";

// Confetti Controller
// <PERSON>les confetti animation for the onboarding completion screen
export default class extends Controller {
  static targets = ["container"];

  connect() {
    console.log("Confetti controller connected");
    
    // Trigger confetti animation after a short delay
    setTimeout(() => {
      this.triggerConfetti();
    }, 500);
  }

  // Trigger confetti animation
  triggerConfetti() {
    if (typeof confetti === 'undefined') {
      console.warn('Confetti library not loaded');
      return;
    }

    // Track completion analytics
    this.trackCompletion();

    // Create multiple bursts of confetti
    this.createConfettiBurst();
  }

  // Create confetti burst animation
  createConfettiBurst() {
    const duration = 3000;
    const animationEnd = Date.now() + duration;
    const defaults = { 
      startVelocity: 30, 
      spread: 360, 
      ticks: 60, 
      zIndex: 1000,
      colors: ['#10b981', '#059669', '#047857', '#065f46'] // Emerald color palette
    };

    const interval = setInterval(() => {
      const timeLeft = animationEnd - Date.now();

      if (timeLeft <= 0) {
        clearInterval(interval);
        return;
      }

      const particleCount = 50 * (timeLeft / duration);

      // Create confetti from different positions
      confetti(Object.assign({}, defaults, {
        particleCount,
        origin: { x: this.randomInRange(0.1, 0.3), y: Math.random() - 0.2 }
      }));
      
      confetti(Object.assign({}, defaults, {
        particleCount,
        origin: { x: this.randomInRange(0.7, 0.9), y: Math.random() - 0.2 }
      }));
    }, 250);

    // Add a final burst from the center
    setTimeout(() => {
      confetti(Object.assign({}, defaults, {
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      }));
    }, 1000);
  }

  // Generate random number in range
  randomInRange(min, max) {
    return Math.random() * (max - min) + min;
  }

  // Track completion analytics
  trackCompletion() {
    if (typeof posthog !== 'undefined') {
      posthog.capture('onboarding_completed', {
        completion_time: Date.now(),
        user_id: this.data.get('userId'),
        organization_id: this.data.get('organizationId')
      });
    }
  }

  // Manual trigger for testing or additional celebrations
  celebrate(event) {
    if (event) {
      event.preventDefault();
    }
    
    this.triggerConfetti();
  }

  // Create a rainbow confetti effect (alternative animation)
  rainbowConfetti() {
    if (typeof confetti === 'undefined') return;

    const colors = ['#ff0000', '#ff8000', '#ffff00', '#80ff00', '#00ff00', '#00ff80', '#00ffff', '#0080ff', '#0000ff', '#8000ff', '#ff00ff', '#ff0080'];
    
    confetti({
      particleCount: 100,
      spread: 70,
      origin: { y: 0.6 },
      colors: colors
    });
  }

  // Create a heart-shaped confetti effect
  heartConfetti() {
    if (typeof confetti === 'undefined') return;

    const heart = confetti.shapeFromText({ text: '💖', scalar: 2 });
    
    confetti({
      shapes: [heart],
      particleCount: 50,
      spread: 100,
      origin: { y: 0.6 },
      colors: ['#ff69b4', '#ff1493', '#dc143c']
    });
  }

  // Cleanup when controller disconnects
  disconnect() {
    // Clear any remaining intervals or timeouts if needed
    console.log("Confetti controller disconnected");
  }
}
