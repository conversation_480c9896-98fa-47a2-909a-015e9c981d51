import { Controller } from "@hotwired/stimulus";

// File Upload Controller
// Handles file upload preview and validation for onboarding forms
export default class extends Controller {
  static targets = ["input", "preview", "error"];

  connect() {
    console.log("FileUpload controller connected");
  }

  // Handle file selection and preview
  preview(event) {
    const file = event.target.files[0];
    
    if (!file) {
      this.clearPreview();
      return;
    }

    // Validate file
    if (!this.validateFile(file)) {
      return;
    }

    // Show preview
    this.showPreview(file);
    
    // Clear any previous errors
    this.clearError();
  }

  // Validate uploaded file
  validateFile(file) {
    const maxSize = 2 * 1024 * 1024; // 2MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];

    // Check file size
    if (file.size > maxSize) {
      this.showError('File size must be less than 2MB');
      return false;
    }

    // Check file type
    if (!allowedTypes.includes(file.type)) {
      this.showError('Please select a valid image file (JPG, PNG, or GIF)');
      return false;
    }

    return true;
  }

  // Show file preview
  showPreview(file) {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      // Find the preview container (the div with the building icon)
      const previewContainer = this.element.querySelector('.w-16.h-16');
      
      if (previewContainer) {
        previewContainer.innerHTML = `
          <img src="${e.target.result}" 
               alt="Logo preview" 
               class="w-full h-full object-cover rounded-lg border border-stone-200">
        `;
      }
    };
    
    reader.readAsDataURL(file);
  }

  // Clear preview
  clearPreview() {
    const previewContainer = this.element.querySelector('.w-16.h-16');
    
    if (previewContainer) {
      previewContainer.innerHTML = `
        <svg class="w-8 h-8 text-stone-400" fill="currentColor" viewBox="0 0 256 256">
          <path d="M208,32H48A16,16,0,0,0,32,48V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V48A16,16,0,0,0,208,32ZM112,176a16,16,0,0,1-16-16V96a16,16,0,0,1,16-16h32a16,16,0,0,1,16,16v64a16,16,0,0,1-16,16Zm32-96H112v64h32Zm64,96a16,16,0,0,1-16-16V96a16,16,0,0,1,16-16h16a16,16,0,0,1,0,32H192v48h16a16,16,0,0,1,0,32Z"></path>
        </svg>
      `;
    }
  }

  // Show error message
  showError(message) {
    // Clear file input
    if (this.hasInputTarget) {
      this.inputTarget.value = '';
    }

    // Clear preview
    this.clearPreview();

    // Find or create error container
    let errorContainer = this.element.querySelector('.file-upload-error');
    
    if (!errorContainer) {
      errorContainer = document.createElement('div');
      errorContainer.className = 'file-upload-error mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700';
      
      // Insert after the file input container
      const inputContainer = this.element.querySelector('.flex.items-center.space-x-4');
      if (inputContainer && inputContainer.parentNode) {
        inputContainer.parentNode.insertBefore(errorContainer, inputContainer.nextSibling);
      }
    }
    
    errorContainer.innerHTML = `
      <div class="flex items-center">
        <svg class="w-4 h-4 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
        </svg>
        ${message}
      </div>
    `;

    // Auto-hide after 5 seconds
    setTimeout(() => {
      this.clearError();
    }, 5000);
  }

  // Clear error message
  clearError() {
    const errorContainer = this.element.querySelector('.file-upload-error');
    if (errorContainer) {
      errorContainer.remove();
    }
  }

  // Handle drag and drop (optional enhancement)
  dragOver(event) {
    event.preventDefault();
    event.stopPropagation();
    
    const dropZone = this.element.querySelector('.w-16.h-16');
    if (dropZone) {
      dropZone.classList.add('border-stone-400', 'bg-stone-50');
    }
  }

  dragLeave(event) {
    event.preventDefault();
    event.stopPropagation();
    
    const dropZone = this.element.querySelector('.w-16.h-16');
    if (dropZone) {
      dropZone.classList.remove('border-stone-400', 'bg-stone-50');
    }
  }

  drop(event) {
    event.preventDefault();
    event.stopPropagation();
    
    const dropZone = this.element.querySelector('.w-16.h-16');
    if (dropZone) {
      dropZone.classList.remove('border-stone-400', 'bg-stone-50');
    }
    
    const files = event.dataTransfer.files;
    if (files.length > 0 && this.hasInputTarget) {
      // Simulate file input change
      this.inputTarget.files = files;
      this.preview({ target: { files: files } });
    }
  }
}
