<!DOCTYPE html>
<html>
  <head>
    <title>Ghostwrote - Welcome</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= favicon_link_tag asset_path('favicon.ico') %>
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= stylesheet_link_tag "application" %>
    <%= javascript_pack_tag "application" %>

<script>
    !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]);t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.async=!0,p.src=s.api_host+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="capture identify alias people.set people.set_once set_config register register_once unregister opt_out_capturing has_opted_out_capturing opt_in_capturing reset isFeatureEnabled onFeatureFlags getFeatureFlag getFeatureFlagPayload reloadFeatureFlags group updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures getActiveMatchingSurveys getSurveys onSessionId".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
    posthog.init('phc_vgVSNt8fZ4lCmxFuGhWK98EiWNgggHYrdlMQbp42JCm', {
        api_host: 'https://us.i.posthog.com',
        person_profiles: 'identified_only', // or 'always' to create profiles for anonymous users as well
    })
</script>

  </head>

  <body class="min-h-screen bg-stone-50">
    <!-- Full screen modal overlay -->
    <div class="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 backdrop-blur-sm">
      <div class="flex items-center justify-center min-h-screen p-4">
        <!-- Modal container -->
        <div class="relative w-full max-w-4xl bg-white rounded-2xl shadow-2xl overflow-hidden">
          <!-- Modal header -->
          <div class="flex items-center justify-between p-6 border-b border-stone-200 bg-stone-50">
            <div class="flex items-center space-x-3">
              <div class="text-2xl font-bold text-stone-900">ghostwrote.</div>
              <div class="px-3 py-1 text-sm font-medium text-stone-600 bg-stone-200 rounded-full">
                Setup
              </div>
            </div>
            
            <!-- Progress indicator will be inserted here -->
            <div id="progress-indicator" class="flex items-center space-x-2">
              <!-- Progress steps will be dynamically updated -->
            </div>
          </div>
          
          <!-- Modal content -->
          <div id="onboarding-content" class="p-8">
            <%= yield %>
          </div>
        </div>
      </div>
    </div>

    <!-- Include confetti library for completion screen -->
    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js"></script>
  </body>
</html>
