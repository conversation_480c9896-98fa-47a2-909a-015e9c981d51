<div class="flex flex-col h-full bg-white border rounded-md border-stone-200">
  <div class="flex-1 w-full px-4 py-8 mx-auto max-w-7xl sm:px-6 lg:px-8">
    <div class="flex flex-col px-16 mb-8">
      <h1 class="text-2xl font-bold text-stone-900">
        Set up your Talent Profile
      </h1>
      <p class="mt-1 text-sm leading-6 text-stone-600">
        Complete your talent profile to start receiving job opportunities. As a scout, you'll have access to both areas of the platform.
      </p>
    </div>

    <%= form_with(model: @profile, url: scout_create_talent_onboarding_path, method: :post, class: "px-16", data: { controller: "profile-form" }) do |f| %>
      <% if @profile.errors.any? %>
        <div class="p-4 mb-6 rounded-md bg-red-50">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg
                class="w-5 h-5 text-red-400"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                Please fix the following issues:
              </h3>
              <div class="mt-2 text-sm text-red-700">
                <ul class="pl-5 space-y-1 list-disc">
                  <% @profile.errors.messages.each do |field, messages| %>
                    <li>
                      <strong><%= field.to_s.humanize %>:</strong>
                      <%= messages.join(', ') %>
                    </li>
                  <% end %>
                </ul>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <div class="space-y-12">
        <div class="grid grid-cols-2 py-12 border-y gap-x-8 gap-y-10 border-stone-900/10 md:grid-cols-3">
          <div>
            <h2 class="text-base font-semibold leading-7 text-stone-900">Profile</h2>
            <p class="mt-1 text-sm leading-6 text-stone-600">This information will be displayed publicly so be careful what you share.</p>
          </div>

          <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6 md:col-span-2">
            <div class="sm:col-span-4">
              <%= f.label :headline, class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.text_field :headline, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6", placeholder: "e.g., Expert Newsletter Writer & Content Strategist" %>
              </div>
            </div>

            <div class="sm:col-span-6">
              <%= f.label :bio, class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.text_area :bio, rows: 3, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6", placeholder: "Write a brief bio about yourself and your expertise..." %>
              </div>
            </div>

            <div class="sm:col-span-6">
              <%= f.label :location, class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.text_field :location, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6", placeholder: "e.g., New York, NY" %>
              </div>
            </div>

            <div class="sm:col-span-6">
              <h3 class="text-sm font-medium leading-6 text-stone-900">Skills</h3>
              <p class="mt-1 text-sm leading-6 text-stone-500">Enter skills separated by commas</p>
              <div class="mt-2">
                <%= f.text_field :skills, value: @profile.skills.is_a?(Array) ? @profile.skills.join(", ") : @profile.skills, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6", placeholder: "e.g., Content Writing, Email Marketing, SEO" %>
              </div>
            </div>

            <div class="sm:col-span-3">
              <%= f.label :availability_status, "Availability", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.select :availability_status, options_for_select([
                  ['Available', 'available'],
                  ['Limited Availability', 'limited'],
                  ['Not Available', 'unavailable']
                ], @profile.availability_status), {}, { class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" } %>
              </div>
            </div>

            <div class="sm:col-span-3">
              <%= f.label :pricing_model, "Pricing Model", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.select :pricing_model, options_for_select([
                  ['Hourly', 'hourly'],
                  ['Fixed Price', 'fixed_price'],
                  ['Retainer', 'retainer'],
                  ['Project Based', 'project_based']
                ], @profile.pricing_model), {}, { class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6" } %>
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-2 pb-12 border-b gap-x-8 gap-y-10 border-stone-900/10 md:grid-cols-3">
          <div>
            <h2 class="text-base font-semibold leading-7 text-stone-900">Portfolio & Links</h2>
            <p class="mt-1 text-sm leading-6 text-stone-600">Share your work and online presence.</p>
          </div>

          <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6 md:col-span-2">
            <div class="sm:col-span-6">
              <%= f.label :portfolio_link, "Portfolio URL", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.url_field :portfolio_link, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6", placeholder: "https://yourportfolio.com" %>
              </div>
            </div>

            <div class="sm:col-span-3">
              <%= f.label :linkedin_url, "LinkedIn URL", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.url_field :linkedin_url, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6", placeholder: "https://linkedin.com/in/yourprofile" %>
              </div>
            </div>

            <div class="sm:col-span-3">
              <%= f.label :website_url, "Website URL", class: "block text-sm font-medium leading-6 text-stone-900" %>
              <div class="mt-2">
                <%= f.url_field :website_url, class: "block w-full rounded-md border-0 py-1.5 text-stone-900 shadow-sm ring-1 ring-inset ring-stone-300 placeholder:text-stone-400 focus:ring-2 focus:ring-inset focus:ring-stone-600 sm:text-sm sm:leading-6", placeholder: "https://yourwebsite.com" %>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="flex items-center justify-between mt-6 gap-x-6">
        <%= link_to "Cancel", launchpad_path, class: "text-sm font-semibold leading-6 text-stone-900" %>
        <%= f.submit "Complete Talent Setup", class: "px-6 py-3 text-sm font-semibold text-white bg-stone-600 rounded-md shadow-sm hover:bg-stone-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-stone-600" %>
      </div>
    <% end %>
  </div>
</div>
