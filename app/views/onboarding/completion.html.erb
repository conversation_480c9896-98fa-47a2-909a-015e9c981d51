<div data-controller="onboarding-flow confetti" data-onboarding-flow-current-step-value="3" class="max-w-2xl mx-auto text-center">
  <!-- Confetti Animation Container -->
  <div data-confetti-target="container" class="fixed inset-0 pointer-events-none z-50"></div>

  <!-- Celebration Header -->
  <div class="mb-8">
    <div class="mb-6">
      <!-- Animated celebration icon -->
      <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full mb-4 animate-bounce">
        <%= phosphor_icon "confetti", class: "w-10 h-10 text-white" %>
      </div>
    </div>
    
    <h1 class="text-4xl font-bold text-stone-900 mb-4">
      You're all set! 🎉
    </h1>
    <p class="text-lg text-stone-600 leading-relaxed mb-6">
      Welcome to Ghostwrote! Your account is ready and you're all set to start your hiring journey.
    </p>
  </div>

  <!-- Progress indicator - All complete -->
  <div class="flex items-center justify-center mb-12">
    <div class="flex items-center space-x-4">
      <div class="flex items-center justify-center w-8 h-8 bg-emerald-500 text-white rounded-full">
        <%= phosphor_icon "check", class: "w-4 h-4" %>
      </div>
      <div class="w-8 h-px bg-emerald-300"></div>
      <div class="flex items-center justify-center w-8 h-8 bg-emerald-500 text-white rounded-full">
        <%= phosphor_icon "check", class: "w-4 h-4" %>
      </div>
      <div class="w-8 h-px bg-emerald-300"></div>
      <div class="flex items-center justify-center w-8 h-8 bg-emerald-500 text-white rounded-full">
        <%= phosphor_icon "check", class: "w-4 h-4" %>
      </div>
      <div class="w-8 h-px bg-emerald-300"></div>
      <div class="flex items-center justify-center w-8 h-8 bg-emerald-500 text-white rounded-full">
        <%= phosphor_icon "check", class: "w-4 h-4" %>
      </div>
    </div>
  </div>

  <!-- Value Reinforcement -->
  <div class="mb-12 p-8 bg-gradient-to-br from-stone-50 to-stone-100 rounded-2xl border border-stone-200">
    <h2 class="text-xl font-semibold text-stone-900 mb-4">
      Dive into our vast pool of 8M+ candidates
    </h2>
    <p class="text-stone-600 mb-6 leading-relaxed">
      Whether you're seeking a seasoned professional or fresh talent, Ghostwrote offers two seamless paths: 
      source talent directly or post a job for free. Your journey to discovering standout hires starts here.
    </p>
    
    <!-- Success metrics -->
    <div class="grid grid-cols-3 gap-6 text-center">
      <div>
        <div class="text-2xl font-bold text-stone-900">8M+</div>
        <div class="text-sm text-stone-600">Active Candidates</div>
      </div>
      <div>
        <div class="text-2xl font-bold text-stone-900">Free</div>
        <div class="text-sm text-stone-600">Job Postings</div>
      </div>
      <div>
        <div class="text-2xl font-bold text-stone-900">AI</div>
        <div class="text-sm text-stone-600">Smart Matching</div>
      </div>
    </div>
  </div>

  <!-- Motivational Text -->
  <div class="mb-12">
    <p class="text-lg font-medium text-stone-800 italic">
      "Your journey to discovering standout hires starts here."
    </p>
  </div>

  <!-- Dual CTA Options -->
  <div class="space-y-4">
    <!-- Primary CTA: Post a Job -->
    <%= form_with url: onboarding_complete_path, method: :post, local: true, class: "mb-4" do |form| %>
      <%= hidden_field_tag :choice, 'post_job' %>
      <%= form.submit "Post a Job", 
          class: "w-full inline-flex items-center justify-center px-8 py-4 bg-stone-900 text-white text-lg font-semibold rounded-xl hover:bg-stone-800 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" %>
    <% end %>

    <!-- Secondary CTA: Find Talent -->
    <%= form_with url: onboarding_complete_path, method: :post, local: true do |form| %>
      <%= hidden_field_tag :choice, 'find_talent' %>
      <%= form.submit "Find Talent", 
          class: "w-full inline-flex items-center justify-center px-8 py-4 bg-white text-stone-900 text-lg font-semibold rounded-xl border-2 border-stone-300 hover:border-stone-400 hover:bg-stone-50 transition-all duration-200 shadow-sm hover:shadow-md" %>
    <% end %>
  </div>

  <!-- Alternative option -->
  <div class="mt-8">
    <%= form_with url: onboarding_complete_path, method: :post, local: true do |form| %>
      <%= hidden_field_tag :choice, 'dashboard' %>
      <%= form.submit "Take me to the dashboard", 
          class: "text-stone-600 hover:text-stone-800 underline text-sm transition-colors" %>
    <% end %>
  </div>
</div>

<!-- Confetti Animation Script -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Trigger confetti animation when page loads
    setTimeout(function() {
      if (typeof confetti !== 'undefined') {
        // Create multiple bursts of confetti
        const duration = 3000;
        const animationEnd = Date.now() + duration;
        const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 1000 };

        function randomInRange(min, max) {
          return Math.random() * (max - min) + min;
        }

        const interval = setInterval(function() {
          const timeLeft = animationEnd - Date.now();

          if (timeLeft <= 0) {
            return clearInterval(interval);
          }

          const particleCount = 50 * (timeLeft / duration);

          // Create confetti from different positions
          confetti(Object.assign({}, defaults, {
            particleCount,
            origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 }
          }));
          confetti(Object.assign({}, defaults, {
            particleCount,
            origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 }
          }));
        }, 250);
      }
    }, 500); // Delay to ensure page is fully loaded
  });
</script>
