<div data-controller="onboarding-flow" data-onboarding-flow-current-step-value="2" class="max-w-2xl mx-auto">
  <!-- Header -->
  <div class="text-center mb-8">
    <h1 class="text-3xl font-bold text-stone-900 mb-3">Invite your team</h1>
    <p class="text-lg text-stone-600">
      Add team members to collaborate on hiring (optional)
    </p>
  </div>

  <!-- Progress indicator -->
  <div class="flex items-center justify-center mb-8">
    <div class="flex items-center space-x-4">
      <div class="flex items-center justify-center w-8 h-8 bg-emerald-500 text-white rounded-full">
        <%= phosphor_icon "check", class: "w-4 h-4" %>
      </div>
      <div class="w-8 h-px bg-stone-300"></div>
      <div class="flex items-center justify-center w-8 h-8 bg-emerald-500 text-white rounded-full">
        <%= phosphor_icon "check", class: "w-4 h-4" %>
      </div>
      <div class="w-8 h-px bg-stone-300"></div>
      <div class="flex items-center justify-center w-8 h-8 bg-stone-900 text-white rounded-full">
        <span class="text-sm font-semibold">3</span>
      </div>
      <div class="w-8 h-px bg-stone-300"></div>
      <div class="flex items-center justify-center w-8 h-8 bg-stone-200 text-stone-400 rounded-full">
        <span class="text-sm font-semibold">4</span>
      </div>
    </div>
  </div>

  <!-- Team Benefits -->
  <div class="mb-8 p-6 bg-stone-50 rounded-xl border border-stone-200">
    <h2 class="text-lg font-semibold text-stone-900 mb-4">Why invite team members?</h2>
    <div class="space-y-3">
      <div class="flex items-start space-x-3">
        <%= phosphor_icon "users-three", class: "w-5 h-5 text-stone-600 mt-0.5 flex-shrink-0" %>
        <p class="text-sm text-stone-600">Collaborate on job postings and candidate reviews</p>
      </div>
      <div class="flex items-start space-x-3">
        <%= phosphor_icon "chat-circle", class: "w-5 h-5 text-stone-600 mt-0.5 flex-shrink-0" %>
        <p class="text-sm text-stone-600">Share feedback and make hiring decisions together</p>
      </div>
      <div class="flex items-start space-x-3">
        <%= phosphor_icon "lightning", class: "w-5 h-5 text-stone-600 mt-0.5 flex-shrink-0" %>
        <p class="text-sm text-stone-600">Streamline your hiring process with team workflows</p>
      </div>
    </div>
  </div>

  <!-- Team Invitation Form -->
  <%= form_with url: onboarding_invite_team_members_path, 
      method: :post,
      class: "space-y-6",
      data: { turbo: true, controller: "team-invites" } do |form| %>
    
    <div>
      <label for="team_emails" class="block text-sm font-medium text-stone-700 mb-2">
        Team Member Email Addresses
      </label>
      <textarea 
        name="team_emails" 
        id="team_emails"
        rows="4"
        placeholder="Enter email addresses separated by commas&#10;e.g., <EMAIL>, <EMAIL>"
        class="w-full px-4 py-3 border border-stone-300 rounded-lg focus:ring-2 focus:ring-stone-500 focus:border-stone-500 transition-colors resize-none"
        data-team-invites-target="emailInput"></textarea>
      <p class="mt-2 text-xs text-stone-500">
        We'll send them an invitation to join <%= @organization&.name || "your organization" %> on Ghostwrote
      </p>
    </div>

    <!-- Preview invited emails -->
    <div data-team-invites-target="preview" class="hidden">
      <h3 class="text-sm font-medium text-stone-700 mb-2">Team members to invite:</h3>
      <div data-team-invites-target="emailList" class="space-y-2">
        <!-- Email previews will be inserted here -->
      </div>
    </div>

    <!-- Form Actions -->
    <div class="flex items-center justify-between pt-6">
      <!-- Skip Option -->
      <%= link_to onboarding_skip_team_setup_path, 
          method: :post,
          class: "inline-flex items-center px-6 py-3 text-stone-600 hover:text-stone-800 font-medium transition-colors",
          data: { turbo: true } do %>
        Skip this step
      <% end %>

      <!-- Invite Button -->
      <%= form.submit "Invite Team & Continue", 
          class: "inline-flex items-center px-8 py-3 bg-stone-900 text-white font-semibold rounded-lg hover:bg-stone-800 transition-colors duration-200 shadow-lg hover:shadow-xl",
          data: { team_invites_target: "submitButton" } %>
    </div>
  <% end %>

  <!-- Alternative: Continue without inviting -->
  <div class="text-center mt-6">
    <p class="text-sm text-stone-500 mb-3">
      You can always invite team members later from your settings
    </p>
    <%= link_to onboarding_skip_team_setup_path, 
        method: :post,
        class: "text-sm text-stone-600 hover:text-stone-800 underline transition-colors",
        data: { turbo: true } do %>
      Continue without inviting anyone
    <% end %>
  </div>
</div>
