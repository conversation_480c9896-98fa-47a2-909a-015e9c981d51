<%
  # Progress indicator for onboarding flow
  # current_step: 0 (welcome), 1 (organization), 2 (team), 3 (completion)
  current_step ||= 0
  
  steps = [
    { label: "Account", completed: true },
    { label: "Company", completed: current_step > 0 },
    { label: "Team", completed: current_step > 1 },
    { label: "Complete", completed: current_step > 2 }
  ]
%>

<div class="flex items-center justify-center mb-8">
  <div class="flex items-center space-x-4">
    <% steps.each_with_index do |step, index| %>
      <% 
        is_active = index == current_step
        is_completed = step[:completed] && !is_active
        
        step_classes = "flex items-center justify-center w-8 h-8 rounded-full text-sm font-semibold"
        
        if is_completed
          step_classes += " bg-emerald-500 text-white"
        elsif is_active
          step_classes += " bg-stone-900 text-white"
        else
          step_classes += " bg-stone-200 text-stone-400"
        end
        
        connector_classes = "w-8 h-px"
        if current_step > index
          connector_classes += " bg-emerald-300"
        else
          connector_classes += " bg-stone-300"
        end
      %>
      
      <% if index > 0 %>
        <div class="<%= connector_classes %>"></div>
      <% end %>
      
      <div class="<%= step_classes %>">
        <% if is_completed %>
          <%= phosphor_icon "check", class: "w-4 h-4" %>
        <% else %>
          <%= index + 1 %>
        <% end %>
      </div>
      
      <% if index < steps.length - 1 %>
        <span class="text-xs font-medium text-stone-600 ml-2"><%= step[:label] %></span>
      <% else %>
        <span class="text-xs font-medium text-stone-600 ml-2"><%= step[:label] %></span>
      <% end %>
    <% end %>
  </div>
</div>
