<div data-controller="onboarding-flow" data-onboarding-flow-current-step-value="1" class="max-w-2xl mx-auto">
  <!-- Header -->
  <div class="text-center mb-8">
    <h1 class="text-3xl font-bold text-stone-900 mb-3">Set up your company</h1>
    <p class="text-lg text-stone-600">
      Tell us about your organization so we can personalize your experience
    </p>
  </div>

  <!-- Progress indicator -->
  <div class="flex items-center justify-center mb-8">
    <div class="flex items-center space-x-4">
      <div class="flex items-center justify-center w-8 h-8 bg-emerald-500 text-white rounded-full">
        <%= phosphor_icon "check", class: "w-4 h-4" %>
      </div>
      <div class="w-8 h-px bg-stone-300"></div>
      <div class="flex items-center justify-center w-8 h-8 bg-stone-900 text-white rounded-full">
        <span class="text-sm font-semibold">2</span>
      </div>
      <div class="w-8 h-px bg-stone-300"></div>
      <div class="flex items-center justify-center w-8 h-8 bg-stone-200 text-stone-400 rounded-full">
        <span class="text-sm font-semibold">3</span>
      </div>
      <div class="w-8 h-px bg-stone-300"></div>
      <div class="flex items-center justify-center w-8 h-8 bg-stone-200 text-stone-400 rounded-full">
        <span class="text-sm font-semibold">4</span>
      </div>
    </div>
  </div>

  <!-- Form -->
  <%= form_with model: @organization, url: onboarding_create_organization_path, 
      class: "space-y-6", 
      data: { turbo: true, controller: "file-upload" } do |form| %>
    
    <% if @organization.errors.any? %>
      <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
        <h3 class="text-sm font-medium text-red-800 mb-2">Please fix the following errors:</h3>
        <ul class="text-sm text-red-700 list-disc list-inside">
          <% @organization.errors.full_messages.each do |message| %>
            <li><%= message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

    <!-- Company Name -->
    <div>
      <%= form.label :name, "Company Name", class: "block text-sm font-medium text-stone-700 mb-2" %>
      <%= form.text_field :name, 
          required: true,
          autofocus: true,
          placeholder: "Enter your company name",
          class: "w-full px-4 py-3 border border-stone-300 rounded-lg focus:ring-2 focus:ring-stone-500 focus:border-stone-500 transition-colors" %>
    </div>

    <!-- Company Logo Upload -->
    <div>
      <%= form.label :logo, "Company Logo (Optional)", class: "block text-sm font-medium text-stone-700 mb-2" %>
      <div class="flex items-center space-x-4">
        <div class="flex items-center justify-center w-16 h-16 bg-stone-100 border-2 border-dashed border-stone-300 rounded-lg">
          <% if @organization.logo.attached? %>
            <%= image_tag @organization.logo, class: "w-full h-full object-cover rounded-lg" %>
          <% else %>
            <%= phosphor_icon "building", class: "w-8 h-8 text-stone-400" %>
          <% end %>
        </div>
        <%= form.file_field :logo, 
            accept: "image/*",
            class: "hidden",
            data: { file_upload_target: "input", action: "change->file-upload#preview" } %>
        <label for="<%= form.field_id(:logo) %>" class="cursor-pointer inline-flex items-center px-4 py-2 border border-stone-300 rounded-lg text-sm font-medium text-stone-700 bg-white hover:bg-stone-50 transition-colors">
          <%= phosphor_icon "upload", class: "w-4 h-4 mr-2" %>
          Choose Logo
        </label>
      </div>
      <p class="mt-1 text-xs text-stone-500">PNG, JPG up to 2MB</p>
    </div>

    <!-- Work Email -->
    <div>
      <%= form.label :email, "Work Email", class: "block text-sm font-medium text-stone-700 mb-2" %>
      <%= form.email_field :email, 
          required: true,
          placeholder: "<EMAIL>",
          class: "w-full px-4 py-3 border border-stone-300 rounded-lg focus:ring-2 focus:ring-stone-500 focus:border-stone-500 transition-colors" %>
      <p class="mt-1 text-xs text-stone-500">We'll use this for important communications</p>
    </div>

    <!-- Company Size -->
    <div>
      <%= form.label :size, "Number of Employees", class: "block text-sm font-medium text-stone-700 mb-2" %>
      <%= form.select :size, 
          [
            ["1-10 employees", "small"],
            ["11-50 employees", "medium"],
            ["51-200 employees", "large"],
            ["201-1000 employees", "xlarge"],
            ["1000+ employees", "enterprise"]
          ],
          { prompt: "Select company size" },
          { required: true, class: "w-full px-4 py-3 border border-stone-300 rounded-lg focus:ring-2 focus:ring-stone-500 focus:border-stone-500 transition-colors" } %>
    </div>

    <!-- Industry Type -->
    <div>
      <%= form.label :industry_type, "Industry (Optional)", class: "block text-sm font-medium text-stone-700 mb-2" %>
      <%= form.text_field :industry_type, 
          placeholder: "e.g., Technology, Healthcare, Finance",
          class: "w-full px-4 py-3 border border-stone-300 rounded-lg focus:ring-2 focus:ring-stone-500 focus:border-stone-500 transition-colors" %>
    </div>

    <!-- Operating Timezone -->
    <div>
      <%= form.label :operating_timezone, "Operating Timezone", class: "block text-sm font-medium text-stone-700 mb-2" %>
      <%= form.time_zone_select :operating_timezone, 
          ActiveSupport::TimeZone.all.sort,
          { prompt: "Select your timezone" },
          { required: true, class: "w-full px-4 py-3 border border-stone-300 rounded-lg focus:ring-2 focus:ring-stone-500 focus:border-stone-500 transition-colors" } %>
    </div>

    <!-- Form Actions -->
    <div class="flex items-center justify-between pt-6">
      <%= link_to onboarding_welcome_path, 
          class: "inline-flex items-center px-6 py-3 text-stone-600 hover:text-stone-800 font-medium transition-colors" do %>
        <%= phosphor_icon "arrow-left", class: "w-4 h-4 mr-2" %>
        Back
      <% end %>

      <%= form.submit "Continue", 
          class: "inline-flex items-center px-8 py-3 bg-stone-900 text-white font-semibold rounded-lg hover:bg-stone-800 transition-colors duration-200 shadow-lg hover:shadow-xl" %>
    </div>
  <% end %>
</div>
