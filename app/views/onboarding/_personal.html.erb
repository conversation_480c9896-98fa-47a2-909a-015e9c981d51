<h1 class="text-2xl font-bold mb-1">Tell us about yourself</h1>
<p class="text-stone-600 text-sm mb-6">We need a few details to get you started.</p>

<!-- Display any validation errors -->
<% if @user.errors.any? %>
  <div class="mb-4 text-red-600">
    <h2>
      <%= pluralize(@user.errors.count, "error") %> prohibited this information from being saved:
    </h2>
    <ul class="list-disc list-inside">
      <% @user.errors.each do |error| %>
        <li><%= error.full_message %></li>
      <% end %>
    </ul>
  </div>
<% end %>

<%= form_with(model: @user, url: onboarding_update_personal_path, method: :post, class: "space-y-4 mb-6") do |form| %>
  <div class="grid grid-cols-2 gap-4">
    <div>
      <%= form.label :first_name, "First Name", class: "block text-sm text-stone-700 font-medium mb-1" %>
      <%= form.text_field :first_name, 
                          required: true, 
                          autofocus: true,
                          class: "w-full border border-stone-500 rounded px-3 py-2 focus:outline-none focus:ring focus:ring-blue-200" %>
    </div>
    
    <div>
      <%= form.label :last_name, "Last Name", class: "block text-sm text-stone-700 font-medium mb-1" %>
      <%= form.text_field :last_name, 
                          required: true,
                          class: "w-full border border-stone-500 rounded px-3 py-2 focus:outline-none focus:ring focus:ring-blue-200" %>
    </div>
  </div>
  
  <div>
    <%= form.label :time_zone, "Time Zone", class: "block text-sm text-stone-700 font-medium mb-1" %>
    <%= form.time_zone_select :time_zone, 
                              ActiveSupport::TimeZone.all.sort, 
                              { default: "UTC" }, 
                              { class: "w-full border border-stone-500 rounded px-3 py-2 focus:outline-none focus:ring focus:ring-blue-200" } %>
  </div>
  
  <div>
    <%= form.label :avatar, "Profile Picture (Optional)", class: "block text-sm text-stone-700 font-medium mb-1" %>
    <%= form.file_field :avatar, 
                        accept: "image/*",
                        class: "w-full border border-stone-500 rounded px-3 py-2 focus:outline-none focus:ring focus:ring-blue-200" %>
  </div>
  
  <div class="pt-4">
    <%= form.submit "Continue", class: "w-full bg-black text-white rounded py-2 px-4 hover:bg-stone-800 focus:outline-none focus:ring focus:ring-stone-500" %>
  </div>
<% end %>
