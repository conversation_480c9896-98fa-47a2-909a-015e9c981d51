<% content_for :layout, 'onboarding_modal' %>

<div data-controller="onboarding-flow" data-onboarding-flow-current-step-value="0" class="max-w-2xl mx-auto text-center">
  <!-- Welcome Header -->
  <div class="mb-8">
    <h1 class="text-4xl font-bold text-stone-900 mb-4">Let's get started</h1>
    <p class="text-lg text-stone-600 leading-relaxed">
      Welcome to Ghostwrote! We'll help you set up your account in just a few simple steps 
      so you can start connecting with amazing talent right away.
    </p>
  </div>

  <!-- Progress Steps Overview -->
  <div class="mb-12">
    <div class="flex items-center justify-center space-x-8">
      <!-- Step 1: Account Setup (Completed) -->
      <div class="flex items-center space-x-3">
        <div class="flex items-center justify-center w-8 h-8 bg-emerald-500 text-white rounded-full">
          <%= phosphor_icon "check", class: "w-4 h-4" %>
        </div>
        <span class="text-sm font-medium text-stone-700">Set up account</span>
      </div>

      <!-- Connector -->
      <div class="w-12 h-px bg-stone-300"></div>

      <!-- Step 2: Company Setup (Current) -->
      <div class="flex items-center space-x-3">
        <div class="flex items-center justify-center w-8 h-8 bg-stone-300 text-stone-600 rounded-full border-2 border-stone-400">
          <span class="text-sm font-semibold">2</span>
        </div>
        <span class="text-sm font-medium text-stone-500">Set up company</span>
      </div>

      <!-- Connector -->
      <div class="w-12 h-px bg-stone-300"></div>

      <!-- Step 3: Team Setup (Future) -->
      <div class="flex items-center space-x-3">
        <div class="flex items-center justify-center w-8 h-8 bg-stone-200 text-stone-400 rounded-full">
          <span class="text-sm font-semibold">3</span>
        </div>
        <span class="text-sm font-medium text-stone-400">Add team members</span>
      </div>

      <!-- Connector -->
      <div class="w-12 h-px bg-stone-300"></div>

      <!-- Step 4: Start Recruiting (Future) -->
      <div class="flex items-center space-x-3">
        <div class="flex items-center justify-center w-8 h-8 bg-stone-200 text-stone-400 rounded-full">
          <span class="text-sm font-semibold">4</span>
        </div>
        <span class="text-sm font-medium text-stone-400">Start recruiting</span>
      </div>
    </div>
  </div>

  <!-- Value Proposition -->
  <div class="mb-12 p-6 bg-stone-50 rounded-xl border border-stone-200">
    <h2 class="text-xl font-semibold text-stone-900 mb-4">What you'll get with Ghostwrote</h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
      <div class="flex items-start space-x-3">
        <%= phosphor_icon "users", class: "w-6 h-6 text-stone-600 mt-1 flex-shrink-0" %>
        <div>
          <h3 class="font-medium text-stone-900 mb-1">Access to 8M+ Candidates</h3>
          <p class="text-sm text-stone-600">Discover talented professionals eager for their perfect match</p>
        </div>
      </div>
      <div class="flex items-start space-x-3">
        <%= phosphor_icon "briefcase", class: "w-6 h-6 text-stone-600 mt-1 flex-shrink-0" %>
        <div>
          <h3 class="font-medium text-stone-900 mb-1">Post Jobs for Free</h3>
          <p class="text-sm text-stone-600">Share your opportunities with our engaged talent community</p>
        </div>
      </div>
      <div class="flex items-start space-x-3">
        <%= phosphor_icon "lightning", class: "w-6 h-6 text-stone-600 mt-1 flex-shrink-0" %>
        <div>
          <h3 class="font-medium text-stone-900 mb-1">Smart Matching</h3>
          <p class="text-sm text-stone-600">AI-powered recommendations to find the perfect fit</p>
        </div>
      </div>
    </div>
  </div>

  <!-- CTA Button -->
  <div class="flex justify-center">
    <%= link_to onboarding_organization_setup_path, 
        class: "inline-flex items-center px-8 py-4 bg-stone-900 text-white text-lg font-semibold rounded-xl hover:bg-stone-800 transition-colors duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",
        data: { action: "click->onboarding-flow#nextStep" } do %>
      Let's get started
      <%= phosphor_icon "arrow-right", class: "w-5 h-5 ml-2" %>
    <% end %>
  </div>
</div>
