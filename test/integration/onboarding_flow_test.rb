require 'test_helper'

class OnboardingFlowTest < ActionDispatch::IntegrationTest
  setup do
    @user = users(:one)
    @user.update!(
      verified: true,
      onboarding_completed: false,
      onboarding_step: 'personal',
      scout_signup_completed: false,
    )
  end

  test 'complete onboarding flow from welcome to completion' do
    sign_in @user

    # Step 1: Welcome screen
    get onboarding_welcome_path
    assert_response :success
    assert_select 'h1', text: "Let's get started"

    # Navigate to organization setup
    get onboarding_organization_setup_path
    assert_response :success
    assert_select 'h1', text: 'Set up your company'

    # Step 2: Create organization
    assert_difference('Organization.count') do
      assert_difference('OrganizationMembership.count') do
        post onboarding_create_organization_path,
             params: {
               organization: {
                 name: 'Test Company',
                 email: '<EMAIL>',
                 operating_timezone: 'America/New_York',
                 size: 'medium',
                 industry_type: 'Technology',
               },
             }
      end
    end

    # Should redirect to team setup
    follow_redirect!
    assert_response :success
    assert_select 'h1', text: 'Invite your team'

    # Verify organization was created correctly
    organization = Organization.last
    assert_equal 'Test Company', organization.name
    assert_equal 'Technology', organization.industry_type

    # Verify user has organization membership
    membership =
      OrganizationMembership.find_by(user: @user, organization: organization)
    assert_not_nil membership
    assert_equal 'owner', membership.org_role

    # Verify user's onboarding step was updated
    @user.reload
    assert_equal 'team_setup', @user.onboarding_step

    # Step 3: Skip team setup
    post onboarding_skip_team_setup_path
    follow_redirect!
    assert_response :success
    assert_select 'h1', text: "You're all set! 🎉"

    # Verify user's onboarding step was updated
    @user.reload
    assert_equal 'completion', @user.onboarding_step

    # Step 4: Complete onboarding
    post onboarding_complete_path, params: { choice: 'post_job' }

    # Verify onboarding completion
    @user.reload
    assert @user.onboarding_completed?
    assert @user.scout_signup_completed?
    assert_not_nil @user.onboarding_completed_at
    assert_equal 'completed', @user.onboarding_step

    # Should redirect to job creation
    assert_redirected_to new_scout_job_path
    assert_equal 'Welcome to Ghostwrote! 🎉', flash[:notice]
  end

  test 'complete onboarding flow with team invitations' do
    sign_in @user

    # Navigate through welcome and organization setup
    get onboarding_welcome_path
    assert_response :success

    # Create organization
    post onboarding_create_organization_path,
         params: {
           organization: {
             name: 'Team Company',
             email: '<EMAIL>',
             operating_timezone: 'America/New_York',
             size: 'large',
           },
         }

    follow_redirect!
    assert_select 'h1', text: 'Invite your team'

    # Invite team members
    assert_difference('User.count', 2) do
      assert_difference('OrganizationMembership.count', 2) do
        post onboarding_invite_team_members_path,
             params: {
               team_emails: '<EMAIL>, <EMAIL>',
             }
      end
    end

    # Verify team members were created
    alice = User.find_by(email: '<EMAIL>')
    bob = User.find_by(email: '<EMAIL>')

    assert_not_nil alice
    assert_not_nil bob
    assert_equal false, alice.verified
    assert_equal false, bob.verified

    # Verify they have organization memberships
    organization = Organization.last
    assert organization.users.include?(alice)
    assert organization.users.include?(bob)

    alice_membership =
      OrganizationMembership.find_by(user: alice, organization: organization)
    bob_membership =
      OrganizationMembership.find_by(user: bob, organization: organization)

    assert_equal 'member', alice_membership.org_role
    assert_equal 'member', bob_membership.org_role

    # Should proceed to completion
    follow_redirect!
    assert_select 'h1', text: "You're all set! 🎉"

    # Complete onboarding with find talent choice
    post onboarding_complete_path, params: { choice: 'find_talent' }

    @user.reload
    assert @user.onboarding_completed?
    assert_redirected_to scout_talent_index_path
  end

  test 'onboarding flow handles validation errors gracefully' do
    sign_in @user

    # Try to create organization with invalid data
    get onboarding_organization_setup_path
    assert_response :success

    assert_no_difference('Organization.count') do
      post onboarding_create_organization_path,
           params: {
             organization: {
               name: '', # Required field missing
               email: 'invalid-email',
               operating_timezone: '',
               size: '',
             },
           }
    end

    # Should stay on organization setup with errors
    assert_response :unprocessable_entity
    assert_select '.text-red-700', text: /can't be blank/

    # User's onboarding step should not change
    @user.reload
    assert_equal 'personal', @user.onboarding_step
  end

  test 'onboarding flow redirects completed users to launchpad' do
    @user.update!(onboarding_completed: true)
    sign_in @user

    # All onboarding screens should redirect to launchpad
    get onboarding_welcome_path
    assert_redirected_to launchpad_path

    get onboarding_organization_setup_path
    assert_redirected_to launchpad_path

    get onboarding_team_setup_path
    assert_redirected_to launchpad_path

    get onboarding_completion_path
    assert_redirected_to launchpad_path
  end

  test 'onboarding flow redirects talent users to launchpad' do
    @user.update!(talent_signup_completed: true)
    sign_in @user

    get onboarding_welcome_path
    assert_redirected_to launchpad_path
  end

  test 'onboarding flow requires email verification' do
    @user.update!(verified: false)
    sign_in @user

    get onboarding_welcome_path
    assert_redirected_to root_path
    assert_equal 'Please verify your email first', flash[:alert]
  end

  test 'onboarding flow handles turbo stream responses' do
    sign_in @user

    # Test organization creation with turbo stream
    post onboarding_create_organization_path,
         params: {
           organization: {
             name: 'Turbo Company',
             email: '<EMAIL>',
             operating_timezone: 'America/New_York',
             size: 'small',
           },
         },
         headers: {
           'Accept' => 'text/vnd.turbo-stream.html',
         }

    assert_response :success
    assert_match 'turbo-stream', response.content_type

    # Test team setup skip with turbo stream
    post onboarding_skip_team_setup_path,
         headers: {
           'Accept' => 'text/vnd.turbo-stream.html',
         }

    assert_response :success
    assert_match 'turbo-stream', response.content_type
  end

  test 'onboarding completion choices redirect correctly' do
    sign_in @user
    create_organization_for_user(@user)
    @user.update!(onboarding_step: 'completion')

    # Test post job choice
    post onboarding_complete_path, params: { choice: 'post_job' }
    assert_redirected_to new_scout_job_path

    # Reset user state
    @user.update!(onboarding_completed: false, onboarding_step: 'completion')

    # Test find talent choice
    post onboarding_complete_path, params: { choice: 'find_talent' }
    assert_redirected_to scout_talent_index_path

    # Reset user state
    @user.update!(onboarding_completed: false, onboarding_step: 'completion')

    # Test dashboard choice
    post onboarding_complete_path, params: { choice: 'dashboard' }
    assert_redirected_to scout_root_path

    # Reset user state
    @user.update!(onboarding_completed: false, onboarding_step: 'completion')

    # Test default choice (no choice parameter)
    post onboarding_complete_path
    assert_redirected_to scout_root_path
  end

  private

  def create_organization_for_user(user)
    organization =
      Organization.create!(
        name: 'Test Organization',
        email: '<EMAIL>',
        operating_timezone: 'America/New_York',
        size: 'medium',
      )

    OrganizationMembership.create!(
      user: user,
      organization: organization,
      org_role: 'owner',
    )

    organization
  end
end
