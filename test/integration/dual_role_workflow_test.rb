require 'test_helper'

class DualRoleWorkflowTest < ActionDispatch::IntegrationTest
  setup do
    # Create organization
    @organization = Organization.create!(name: 'Test Organization')

    # Find or create roles
    @scout_role = Role.find_or_create_by!(name: 'scout')
    @talent_role = Role.find_or_create_by!(name: 'talent')

    # Create scout user who has completed scout onboarding
    @scout_user =
      User.create!(
        email: "scout_#{SecureRandom.hex(8)}@example.com",
        password: 'Secret1*3*5*',
        password_confirmation: 'Secret1*3*5*',
        first_name: 'Test',
        last_name: 'Scout',
        time_zone: 'America/New_York',
        verified: true,
        signup_intent: 'scout',
        scout_signup_completed: true,
        talent_signup_completed: false,
        onboarding_step: 'completed',
        onboarding_completed: true,
      )

    # Assign scout role
    UserRole.create!(user: @scout_user, role: @scout_role)

    # Create organization membership
    OrganizationMembership.create!(
      user: @scout_user,
      organization: @organization,
      org_role: 'member',
    )
    @scout_user.update!(last_logged_in_organization_id: @organization.id)
  end

  test 'complete dual role workflow from scout to talent' do
    # Step 1: Sign in as scout user
    sign_in_as @scout_user

    # Step 2: Visit launchpad and verify scout card is shown but not talent card
    get launchpad_path
    assert_response :success

    # Should show scout card
    assert_select 'a[href=?]', scout_root_path
    assert_select 'h2', text: 'Scout Area'

    # Should not show talent card (since talent_signup_completed is false)
    assert_select 'a[href=?]', talent_root_path, count: 0

    # Should show talent onboarding card
    assert_select 'a[href=?]', scout_new_talent_onboarding_path
    assert_select 'h2', text: 'Become a Talent'

    # Step 3: Access scout area successfully
    get scout_root_path
    assert_response :success

    # Step 4: Try to access talent area (should be redirected)
    get talent_root_path
    assert_redirected_to launchpad_path
    follow_redirect!
    assert_select '.flash.alert',
                  text: /You need to complete Talent signup to access this area/

    # Step 5: Click on talent onboarding card
    get scout_new_talent_onboarding_path
    assert_response :success
    assert_select 'h1', text: 'Set up your Talent Profile'

    # Step 6: Complete talent onboarding
    talent_params = {
      talent_profile: {
        headline: 'Expert Content Writer & Strategist',
        bio: 'I specialize in creating engaging content that drives results.',
        location: 'New York, NY',
        skills: 'Content Writing, SEO, Email Marketing, Social Media',
        availability_status: 'available',
        pricing_model: 'hourly',
        portfolio_link: 'https://example.com/portfolio',
        linkedin_url: 'https://linkedin.com/in/testscout',
      },
    }

    post scout_create_talent_onboarding_path, params: talent_params
    assert_redirected_to launchpad_path

    # Step 7: Verify user now has both roles
    @scout_user.reload
    assert @scout_user.scout_signup_completed?
    assert @scout_user.talent_signup_completed?
    assert @scout_user.has_role?(:scout)
    assert @scout_user.has_role?(:talent)

    # Step 8: Visit launchpad and verify both cards are shown
    follow_redirect!
    assert_response :success

    # Should show both scout and talent cards
    assert_select 'a[href=?]', scout_root_path
    assert_select 'a[href=?]', talent_root_path
    assert_select 'h2', text: 'Scout Area'
    assert_select 'h2', text: 'Talent Area'

    # Should not show talent onboarding card anymore
    assert_select 'a[href=?]', scout_new_talent_onboarding_path, count: 0
    assert_select 'h2', text: 'Become a Talent', count: 0

    # Step 9: Access both areas successfully
    get scout_root_path
    assert_response :success

    get talent_root_path
    assert_response :success

    # Step 10: Verify talent profile was created with correct data
    talent_profile = @scout_user.talent_profile
    assert_not_nil talent_profile
    assert_equal 'Expert Content Writer & Strategist', talent_profile.headline
    assert_equal 'I specialize in creating engaging content that drives results.',
                 talent_profile.bio
    assert_equal 'New York, NY', talent_profile.location
    assert_includes talent_profile.skills, 'Content Writing'
    assert_equal 'available', talent_profile.availability_status
    assert_equal 'hourly', talent_profile.pricing_model
  end

  test 'launchpad grid layout adjusts correctly for dual role users' do
    sign_in_as @scout_user

    # Initially: 1 card (scout) + 1 onboarding card = 2 total
    get launchpad_path
    assert_response :success

    # Should use 2-column grid
    assert_select '.grid-cols-2'

    # Complete talent onboarding
    talent_params = {
      talent_profile: {
        headline: 'Content Writer',
        bio: 'I write content',
        location: 'NYC',
        skills: 'Writing',
        availability_status: 'available',
        pricing_model: 'hourly',
      },
    }

    post scout_create_talent_onboarding_path, params: talent_params
    assert_redirected_to launchpad_path

    # Now: 2 cards (scout + talent) = 2 total
    follow_redirect!
    assert_response :success

    # Should still use 2-column grid
    assert_select '.grid-cols-2'
  end

  test 'user cannot access talent onboarding if already completed' do
    # Complete talent signup
    @scout_user.update!(talent_signup_completed: true)
    UserRole.create!(user: @scout_user, role: @talent_role)

    sign_in_as @scout_user

    # Try to access talent onboarding
    get scout_new_talent_onboarding_path
    assert_redirected_to launchpad_path
    follow_redirect!
    assert_select '.flash.notice',
                  text: /You have already completed Talent onboarding/
  end

  test 'user without scout signup cannot access talent onboarding' do
    # Create user without scout signup
    incomplete_user =
      User.create!(
        email: "incomplete_#{SecureRandom.hex(8)}@example.com",
        password: 'Secret1*3*5*',
        password_confirmation: 'Secret1*3*5*',
        first_name: 'Incomplete',
        last_name: 'User',
        verified: true,
        signup_intent: 'scout',
        scout_signup_completed: false,
        onboarding_step: 'completed',
        onboarding_completed: true,
      )

    OrganizationMembership.create!(
      user: incomplete_user,
      organization: @organization,
      org_role: 'member',
    )
    incomplete_user.update!(last_logged_in_organization_id: @organization.id)

    sign_in_as incomplete_user

    get scout_new_talent_onboarding_path
    assert_redirected_to launchpad_path
    follow_redirect!
    assert_select '.flash.alert',
                  text: /You need to complete Scout signup to access this area/
  end
end
