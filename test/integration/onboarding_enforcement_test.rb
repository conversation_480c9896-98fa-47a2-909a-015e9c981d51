require 'test_helper'

class OnboardingEnforcementTest < ActionDispatch::IntegrationTest
  setup do
    @incomplete_user = users(:one)
    @incomplete_user.update!(
      verified: true,
      onboarding_completed: false,
      onboarding_step: 'personal',
      scout_signup_completed: false,
      talent_signup_completed: false,
    )

    @completed_user = users(:scout)
    @completed_user.update!(
      verified: true,
      onboarding_completed: true,
      onboarding_step: 'completed',
      scout_signup_completed: true,
    )

    @talent_user = users(:talent)
    @talent_user.update!(
      verified: true,
      onboarding_completed: false,
      talent_signup_completed: true,
    )
  end

  # Test that incomplete onboarding users are redirected
  test 'incomplete onboarding user redirected from scout pages' do
    sign_in @incomplete_user

    # Test various scout pages
    scout_pages = [
      scout_root_path,
      scout_talent_index_path,
      new_scout_job_path,
      scout_jobs_path,
    ]

    scout_pages.each do |path|
      get path
      assert_redirected_to onboarding_welcome_path,
                           "Expected redirect from #{path} to onboarding welcome"
      assert_equal 'Please complete your setup to continue', flash[:alert]
    end
  end

  test 'incomplete onboarding user redirected from admin pages' do
    @incomplete_user.update!(super_admin: true)
    sign_in @incomplete_user

    get admin_root_path
    assert_redirected_to onboarding_welcome_path
    assert_equal 'Please complete your setup to continue', flash[:alert]
  end

  test 'completed onboarding user can access scout pages' do
    sign_in @completed_user

    get scout_root_path
    assert_response :success

    get scout_talent_index_path
    assert_response :success

    get new_scout_job_path
    assert_response :success
  end

  test 'talent user can access talent pages without scout onboarding' do
    sign_in @talent_user

    get talent_root_path
    assert_response :success

    get talent_jobs_path
    assert_response :success
  end

  test 'talent user redirected from scout pages' do
    sign_in @talent_user

    get scout_root_path
    assert_redirected_to onboarding_welcome_path
    assert_equal 'Please complete your setup to continue', flash[:alert]
  end

  # Test that certain pages are exempt from onboarding enforcement
  test 'onboarding pages are accessible to incomplete users' do
    sign_in @incomplete_user

    onboarding_pages = [
      onboarding_welcome_path,
      onboarding_organization_setup_path,
      onboarding_team_setup_path,
      onboarding_completion_path,
    ]

    onboarding_pages.each do |path|
      get path
      assert_response :success, "Expected success for #{path}"
    end
  end

  test 'launchpad is accessible to incomplete users' do
    sign_in @incomplete_user

    get launchpad_path
    assert_response :success
  end

  test 'identity pages are accessible to incomplete users' do
    sign_in @incomplete_user

    # Test password reset pages
    get new_identity_password_reset_path
    assert_response :success

    # Test email verification pages (if accessible)
    # Note: These might require specific tokens, so we test what we can
  end

  test 'registration pages are accessible to incomplete users' do
    # Don't sign in for registration tests
    get new_registration_path
    assert_response :success

    get new_registration_path(signup_intent: 'scout')
    assert_response :success
  end

  # Test session controller redirects
  test 'login redirects incomplete user to onboarding' do
    post session_path,
         params: {
           email: @incomplete_user.email,
           password: 'password',
         }

    assert_redirected_to onboarding_welcome_path
  end

  test 'login redirects completed user to launchpad' do
    post session_path,
         params: {
           email: @completed_user.email,
           password: 'password',
         }

    assert_redirected_to launchpad_path
  end

  test 'login redirects talent user to launchpad' do
    post session_path,
         params: {
           email: @talent_user.email,
           password: 'password',
         }

    assert_redirected_to launchpad_path
  end

  # Test email verification redirects
  test 'email verification redirects to onboarding for incomplete users' do
    @incomplete_user.update!(verified: false)
    token = @incomplete_user.generate_token_for(:email_verification)

    get identity_email_verification_path(sid: token)

    @incomplete_user.reload
    assert @incomplete_user.verified?
    assert_redirected_to onboarding_welcome_path
    assert_equal 'Thank you for verifying your email address', flash[:notice]
  end

  # Test that API endpoints might have different behavior
  test 'API endpoints should not redirect to onboarding' do
    sign_in @incomplete_user

    # Test API endpoints if they exist
    # This depends on your API structure
    # Example:
    # get api_v1_jobs_path, headers: { 'Accept' => 'application/json' }
    # assert_response :unauthorized # or whatever your API returns
  end

  # Test edge cases
  test 'user with both scout and talent signup completed can access both areas' do
    user = users(:admin)
    user.update!(
      verified: true,
      onboarding_completed: true,
      scout_signup_completed: true,
      talent_signup_completed: true,
    )
    sign_in user

    get scout_root_path
    assert_response :success

    get talent_root_path
    assert_response :success
  end

  test 'unverified user redirected from all protected pages' do
    @incomplete_user.update!(verified: false)
    sign_in @incomplete_user

    get scout_root_path
    assert_redirected_to root_path
    assert_equal 'Please verify your email first', flash[:alert]

    get onboarding_welcome_path
    assert_redirected_to root_path
    assert_equal 'Please verify your email first', flash[:alert]
  end

  test 'unauthenticated user redirected to login' do
    # Don't sign in

    get scout_root_path
    assert_redirected_to new_session_path

    get onboarding_welcome_path
    assert_redirected_to new_session_path
  end

  # Test that onboarding enforcement doesn't interfere with normal operations
  test 'completed user can perform normal actions' do
    sign_in @completed_user

    # Test that they can access various features
    get scout_root_path
    assert_response :success

    # Test that they can create jobs (if the route exists)
    get new_scout_job_path
    assert_response :success

    # Test that they can view talent
    get scout_talent_index_path
    assert_response :success
  end

  test 'onboarding enforcement respects controller exclusions' do
    sign_in @incomplete_user

    # Test that certain controllers are excluded
    # These should be accessible even with incomplete onboarding

    # Launchpad
    get launchpad_path
    assert_response :success

    # Registration (though user is already signed in)
    get new_registration_path
    assert_response :success
  end

  # Test redirect loop prevention
  test "onboarding pages don't create redirect loops" do
    sign_in @incomplete_user

    # Accessing onboarding pages should work normally
    get onboarding_welcome_path
    assert_response :success

    get onboarding_organization_setup_path
    assert_response :success
  end

  test 'enforcement works with different HTTP methods' do
    sign_in @incomplete_user

    # Test POST requests
    post scout_jobs_path, params: { job: { title: 'Test Job' } }
    assert_redirected_to onboarding_welcome_path

    # Test PATCH requests (if applicable)
    # patch some_scout_path(id: 1), params: { some_param: "value" }
    # assert_redirected_to onboarding_welcome_path

    # Test DELETE requests (if applicable)
    # delete some_scout_path(id: 1)
    # assert_redirected_to onboarding_welcome_path
  end
end
