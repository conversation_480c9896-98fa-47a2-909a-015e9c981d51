require 'application_system_test_case'

class OnboardingMobileTest < ApplicationSystemTestCase
  setup do
    @user = users(:one)
    @user.update!(
      verified: true,
      onboarding_completed: false,
      onboarding_step: 'personal',
      scout_signup_completed: false,
    )
  end

  test 'onboarding flow works on mobile viewport' do
    # Set mobile viewport
    page.driver.browser.manage.window.resize_to(375, 667) # iPhone SE size

    sign_in @user
    visit onboarding_welcome_path

    # Test welcome screen mobile layout
    assert_selector 'h1', text: "Let's get started"
    assert_selector '.max-w-2xl' # Responsive container

    # Check that progress indicator is visible and properly spaced
    assert_selector '.flex.items-center.justify-center'

    # Check that CTA button is properly sized for mobile
    assert_selector "a[href='#{onboarding_organization_setup_path}']"

    # Navigate to organization setup
    click_link "Let's get started"

    # Test organization setup form on mobile
    assert_selector 'h1', text: 'Set up your company'

    # Check form fields are properly sized for mobile
    assert_selector "input[name='organization[name]']"
    assert_selector "input[name='organization[email]']"
    assert_selector "select[name='organization[size]']"

    # Fill out form
    fill_in 'Company Name', with: 'Mobile Test Company'
    fill_in 'Work Email', with: '<EMAIL>'
    select '11-50 employees', from: 'Number of Employees'
    select 'America/New_York', from: 'Operating Timezone'
    fill_in 'Industry (Optional)', with: 'Technology'

    # Submit form
    click_button 'Continue'

    # Should navigate to team setup
    assert_selector 'h1', text: 'Invite your team'

    # Test team setup form on mobile
    assert_selector "textarea[name='team_emails']"

    # Skip team setup
    click_link 'Skip this step'

    # Should navigate to completion
    assert_selector 'h1', text: "You're all set! 🎉"

    # Test completion screen buttons on mobile
    assert_selector "input[value='Post a Job']"
    assert_selector "input[value='Find Talent']"

    # Complete onboarding
    click_button 'Post a Job'

    # Should redirect to job creation
    assert_current_path new_scout_job_path
  end

  test 'onboarding modal is responsive on tablet viewport' do
    # Set tablet viewport
    page.driver.browser.manage.window.resize_to(768, 1024) # iPad size

    sign_in @user
    visit onboarding_welcome_path

    # Test that modal container adapts to tablet size
    assert_selector '.max-w-4xl' # Modal container
    assert_selector '.max-w-2xl' # Content container

    # Check that layout elements are properly spaced
    assert_selector '.mb-8' # Header spacing
    assert_selector '.mb-12' # Section spacing

    # Test form layout on tablet
    click_link "Let's get started"

    # Form should be well-spaced on tablet
    assert_selector '.space-y-6' # Form field spacing
    assert_selector ".grid.grid-cols-1.md\\:grid-cols-3" # Value proposition grid
  end

  test 'onboarding works on small mobile viewport' do
    # Set very small mobile viewport
    page.driver.browser.manage.window.resize_to(320, 568) # iPhone 5 size

    sign_in @user
    visit onboarding_welcome_path

    # Content should still be accessible
    assert_selector 'h1', text: "Let's get started"

    # Progress indicator should adapt
    assert_selector '.flex.items-center.space-x-4'

    # Navigate through flow
    click_link "Let's get started"

    # Form should be usable on small screen
    fill_in 'Company Name', with: 'Small Screen Co'
    fill_in 'Work Email', with: '<EMAIL>'
    select '1-10 employees', from: 'Number of Employees'
    select 'America/New_York', from: 'Operating Timezone'

    click_button 'Continue'

    # Team setup should work
    assert_selector 'h1', text: 'Invite your team'
    click_link 'Skip this step'

    # Completion should work
    assert_selector 'h1', text: "You're all set! 🎉"
    click_button 'Find Talent'

    assert_current_path scout_talent_index_path
  end

  test 'form validation errors display properly on mobile' do
    page.driver.browser.manage.window.resize_to(375, 667)

    sign_in @user
    visit onboarding_organization_setup_path

    # Submit form with missing required fields
    click_button 'Continue'

    # Error messages should be visible and properly formatted on mobile
    assert_selector '.bg-red-50.border.border-red-200' # Error container
    assert_selector '.text-red-700', text: /can't be blank/

    # Form should remain usable after validation errors
    fill_in 'Company Name', with: 'Error Test Company'
    fill_in 'Work Email', with: '<EMAIL>'
    select 'Medium (11-50)', from: 'Number of Employees'
    select 'America/New_York', from: 'Operating Timezone'

    click_button 'Continue'

    # Should proceed successfully
    assert_selector 'h1', text: 'Invite your team'
  end

  test 'file upload works on mobile' do
    page.driver.browser.manage.window.resize_to(375, 667)

    sign_in @user
    visit onboarding_organization_setup_path

    # Test that file upload button is accessible on mobile
    assert_selector "label[for*='organization_logo']", text: 'Choose Logo'

    # The file upload preview area should be properly sized
    assert_selector '.w-16.h-16' # Logo preview container
  end

  test 'team invitation interface works on mobile' do
    page.driver.browser.manage.window.resize_to(375, 667)

    sign_in @user

    # Create organization first
    visit onboarding_organization_setup_path
    fill_in 'Company Name', with: 'Team Mobile Co'
    fill_in 'Work Email', with: '<EMAIL>'
    select '11-50 employees', from: 'Number of Employees'
    select 'America/New_York', from: 'Operating Timezone'
    click_button 'Continue'

    # Test team invitation on mobile
    assert_selector 'h1', text: 'Invite your team'

    # Textarea should be properly sized for mobile
    assert_selector "textarea[name='team_emails']"

    # Fill in team emails
    fill_in 'team_emails', with: '<EMAIL>, <EMAIL>'

    # Submit button should be accessible
    click_button 'Invite Team & Continue'

    # Should proceed to completion
    assert_selector 'h1', text: "You're all set! 🎉"
  end

  test 'progress indicator adapts to mobile' do
    page.driver.browser.manage.window.resize_to(375, 667)

    sign_in @user
    visit onboarding_welcome_path

    # Progress indicator should be visible and properly spaced on mobile
    within '.flex.items-center.justify-center' do
      assert_selector '.w-8.h-8', count: 4 # 4 progress steps
      assert_selector '.w-8.h-px', count: 3 # 3 connectors
    end

    # Step labels should be readable on mobile
    assert_text 'Set up account'
    assert_text 'Set up company'
    assert_text 'Add team members'
    assert_text 'Start recruiting'
  end

  test 'completion screen confetti and buttons work on mobile' do
    page.driver.browser.manage.window.resize_to(375, 667)

    sign_in @user

    # Navigate to completion screen
    visit onboarding_organization_setup_path
    fill_in 'Company Name', with: 'Confetti Co'
    fill_in 'Work Email', with: '<EMAIL>'
    select '1-10 employees', from: 'Number of Employees'
    select 'America/New_York', from: 'Operating Timezone'
    click_button 'Continue'

    click_link 'Skip this step'

    # Test completion screen on mobile
    assert_selector 'h1', text: "You're all set! 🎉"

    # Celebration icon should be visible
    assert_selector '.w-20.h-20' # Celebration icon container

    # CTA buttons should be properly sized for mobile
    assert_selector "input[value='Post a Job'].w-full"
    assert_selector "input[value='Find Talent'].w-full"

    # Buttons should be stacked vertically with proper spacing
    assert_selector '.space-y-4'
  end

  private

  def sign_in(user)
    visit new_session_path
    fill_in 'Email', with: user.email
    fill_in 'Password', with: 'password'
    click_button 'Sign in'
  end
end
