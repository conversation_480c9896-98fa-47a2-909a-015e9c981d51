require 'test_helper'

class TalentProfilePrepopulationTest < ActiveSupport::TestCase
  setup do
    @scout_role = Role.find_or_create_by!(name: 'scout')
    @organization = Organization.create!(name: 'Test Organization')
  end

  test 'pre-populates location based on Eastern time zone' do
    user = create_scout_user(time_zone: 'America/New_York')
    controller = create_controller_instance(user)
    
    profile = user.build_talent_profile
    controller.instance_variable_set(:@profile, profile)
    controller.send(:pre_populate_profile_from_scout_data)
    
    assert_equal 'New York, NY', profile.location
    assert_equal 'north_america', profile.location_preference
  end

  test 'pre-populates location based on Pacific time zone' do
    user = create_scout_user(time_zone: 'America/Los_Angeles')
    controller = create_controller_instance(user)
    
    profile = user.build_talent_profile
    controller.instance_variable_set(:@profile, profile)
    controller.send(:pre_populate_profile_from_scout_data)
    
    assert_equal 'Los Angeles, CA', profile.location
    assert_equal 'north_america', profile.location_preference
  end

  test 'pre-populates location based on European time zone' do
    user = create_scout_user(time_zone: 'Europe/London')
    controller = create_controller_instance(user)
    
    profile = user.build_talent_profile
    controller.instance_variable_set(:@profile, profile)
    controller.send(:pre_populate_profile_from_scout_data)
    
    assert_equal 'London, UK', profile.location
    assert_equal 'europe', profile.location_preference
  end

  test 'pre-populates location based on Asian time zone' do
    user = create_scout_user(time_zone: 'Asia/Tokyo')
    controller = create_controller_instance(user)
    
    profile = user.build_talent_profile
    controller.instance_variable_set(:@profile, profile)
    controller.send(:pre_populate_profile_from_scout_data)
    
    assert_equal 'Asia', profile.location
    assert_equal 'asia', profile.location_preference
  end

  test 'handles unknown time zone gracefully' do
    user = create_scout_user(time_zone: 'Unknown/Timezone')
    controller = create_controller_instance(user)
    
    profile = user.build_talent_profile
    controller.instance_variable_set(:@profile, profile)
    controller.send(:pre_populate_profile_from_scout_data)
    
    assert_equal 'Unknown Timezone', profile.location
    assert_equal 'north_america', profile.location_preference # Default fallback
  end

  test 'pre-populates headline with user name' do
    user = create_scout_user(first_name: 'John', last_name: 'Doe')
    controller = create_controller_instance(user)
    
    profile = user.build_talent_profile
    controller.instance_variable_set(:@profile, profile)
    controller.send(:pre_populate_profile_from_scout_data)
    
    assert_equal 'John Doe - Content Creator & Writer', profile.headline
  end

  test 'does not set headline if name is incomplete' do
    user = create_scout_user(first_name: 'John', last_name: nil)
    controller = create_controller_instance(user)
    
    profile = user.build_talent_profile
    controller.instance_variable_set(:@profile, profile)
    controller.send(:pre_populate_profile_from_scout_data)
    
    assert_nil profile.headline
  end

  test 'sets default availability and pricing model' do
    user = create_scout_user
    controller = create_controller_instance(user)
    
    profile = user.build_talent_profile
    controller.instance_variable_set(:@profile, profile)
    controller.send(:pre_populate_profile_from_scout_data)
    
    assert_equal 'available', profile.availability_status
    assert_equal 'hourly', profile.pricing_model
  end

  test 'handles nil time zone gracefully' do
    user = create_scout_user(time_zone: nil)
    controller = create_controller_instance(user)
    
    profile = user.build_talent_profile
    controller.instance_variable_set(:@profile, profile)
    controller.send(:pre_populate_profile_from_scout_data)
    
    assert_nil profile.location
    assert_equal 'north_america', profile.location_preference # Default fallback
  end

  test 'map_timezone_to_location_preference method works correctly' do
    controller = create_controller_instance(create_scout_user)
    
    # Test North America
    assert_equal 'north_america', controller.send(:map_timezone_to_location_preference, 'America/New_York')
    assert_equal 'north_america', controller.send(:map_timezone_to_location_preference, 'US/Pacific')
    assert_equal 'north_america', controller.send(:map_timezone_to_location_preference, 'Canada/Eastern')
    
    # Test Europe
    assert_equal 'europe', controller.send(:map_timezone_to_location_preference, 'Europe/London')
    assert_equal 'europe', controller.send(:map_timezone_to_location_preference, 'Europe/Paris')
    assert_equal 'europe', controller.send(:map_timezone_to_location_preference, 'Europe/Berlin')
    
    # Test Asia
    assert_equal 'asia', controller.send(:map_timezone_to_location_preference, 'Asia/Tokyo')
    assert_equal 'asia', controller.send(:map_timezone_to_location_preference, 'Asia/Shanghai')
    assert_equal 'asia', controller.send(:map_timezone_to_location_preference, 'Asia/Mumbai')
    
    # Test Africa
    assert_equal 'africa', controller.send(:map_timezone_to_location_preference, 'Africa/Cairo')
    
    # Test Australia (maps to Asia as closest option)
    assert_equal 'asia', controller.send(:map_timezone_to_location_preference, 'Australia/Sydney')
    
    # Test unknown/default
    assert_equal 'north_america', controller.send(:map_timezone_to_location_preference, 'Unknown/Zone')
    assert_equal 'north_america', controller.send(:map_timezone_to_location_preference, nil)
  end

  test 'map_timezone_to_location method works correctly' do
    controller = create_controller_instance(create_scout_user)
    
    # Test specific mappings
    assert_equal 'New York, NY', controller.send(:map_timezone_to_location, 'America/New_York')
    assert_equal 'Chicago, IL', controller.send(:map_timezone_to_location, 'America/Chicago')
    assert_equal 'Denver, CO', controller.send(:map_timezone_to_location, 'America/Denver')
    assert_equal 'Los Angeles, CA', controller.send(:map_timezone_to_location, 'America/Los_Angeles')
    assert_equal 'London, UK', controller.send(:map_timezone_to_location, 'Europe/London')
    assert_equal 'Europe', controller.send(:map_timezone_to_location, 'Europe/Paris')
    assert_equal 'Asia', controller.send(:map_timezone_to_location, 'Asia/Tokyo')
    
    # Test fallback behavior
    assert_equal 'Custom Timezone', controller.send(:map_timezone_to_location, 'Custom_Timezone')
    assert_nil controller.send(:map_timezone_to_location, nil)
  end

  private

  def create_scout_user(attributes = {})
    default_attributes = {
      email: "scout_#{SecureRandom.hex(8)}@example.com",
      password: 'password123123',
      password_confirmation: 'password123123',
      first_name: 'Test',
      last_name: 'Scout',
      time_zone: 'America/New_York',
      verified: true,
      signup_intent: 'scout',
      scout_signup_completed: true,
      talent_signup_completed: false,
      onboarding_step: 'completed',
      onboarding_completed: true
    }

    user = User.create!(default_attributes.merge(attributes))
    UserRole.create!(user: user, role: @scout_role)
    
    OrganizationMembership.create!(
      user: user,
      organization: @organization,
      org_role: 'member'
    )
    user.update!(last_logged_in_organization_id: @organization.id)
    
    user
  end

  def create_controller_instance(user)
    controller = Scout::TalentOnboardingController.new
    controller.instance_variable_set(:@current_user, user)
    
    # Mock Current.user to return our test user
    Current.stub(:user, user) do
      controller
    end
  end
end
