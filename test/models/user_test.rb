# == Schema Information
#
# Table name: users
#
#  id                             :bigint           not null, primary key
#  conversations_count            :integer          default(0), not null
#  email                          :string           not null
#  first_name                     :string
#  last_name                      :string
#  onboarding_completed           :boolean          default(FALSE)
#  onboarding_completed_at        :datetime
#  onboarding_step                :string           default("personal")
#  password_digest                :string           not null
#  received_chat_requests_count   :integer          default(0), not null
#  scout_signup_completed         :boolean          default(FALSE)
#  sent_chat_requests_count       :integer          default(0), not null
#  signup_intent                  :string
#  talent_signup_completed        :boolean          default(FALSE)
#  time_zone                      :string
#  verification_email_sent_at     :datetime
#  verified                       :boolean          default(FALSE), not null
#  created_at                     :datetime         not null
#  updated_at                     :datetime         not null
#  last_logged_in_organization_id :integer
#
# Indexes
#
#  index_users_on_email                           (email) UNIQUE
#  index_users_on_first_name                      (first_name)
#  index_users_on_first_name_and_last_name        (first_name,last_name)
#  index_users_on_last_logged_in_organization_id  (last_logged_in_organization_id)
#  index_users_on_last_name                       (last_name)
#  index_users_on_onboarding_completed            (onboarding_completed)
#  index_users_on_onboarding_completed_at         (onboarding_completed_at)
#  index_users_on_scout_signup_completed          (scout_signup_completed)
#  index_users_on_talent_signup_completed         (talent_signup_completed)
#  index_users_on_verified                        (verified)
#  index_users_on_verified_and_created_at         (verified,created_at)
#
require 'test_helper'

class UserTest < ActiveSupport::TestCase
  def setup
    # Clean up first to avoid conflicts
    BadgeAssignment.delete_all
    BadgeType.delete_all

    # Create test badge types with unique names
    @badge_type_premium =
      BadgeType.create!(
        name: 'User Test Premium Writer',
        description: 'A premium writer badge for user tests',
        background_color: '#000000',
        text_color: '#ffffff',
        icon: 'star',
        priority: 1,
        active: true,
      )

    @badge_type_expert =
      BadgeType.create!(
        name: 'User Test Expert',
        description: 'An expert badge for user tests',
        background_color: '#000000',
        text_color: '#ffffff',
        icon: 'crown',
        priority: 2,
        active: true,
      )

    @badge_type_verified =
      BadgeType.create!(
        name: 'User Test Verified',
        description: 'A verified badge for user tests',
        background_color: '#000000',
        text_color: '#ffffff',
        icon: 'check',
        priority: 3,
        active: true,
      )

    # Create test users
    @user = users(:talent)
    @admin = users(:super_admin)
    @other_user = users(:scout)

    # Create test badge assignments
    @active_assignment =
      BadgeAssignment.create!(
        badge_type: @badge_type_premium,
        user: @user,
        admin: @admin,
        assigned_at: Time.current,
        notes: 'Active premium badge',
      )

    @permanent_assignment =
      BadgeAssignment.create!(
        badge_type: @badge_type_verified,
        user: @user,
        admin: @admin,
        assigned_at: 1.day.ago,
        expires_at: nil,
        notes: 'Permanent verified badge',
      )

    # Create expired assignment using test helper
    @expired_assignment =
      BadgeAssignment.new(
        badge_type: @badge_type_expert,
        user: @user,
        admin: @admin,
        assigned_at: 3.days.ago,
        expires_at: 1.day.ago,
        notes: 'Expired expert badge',
      ).allow_expired_for_testing!
    @expired_assignment.save!
  end

  def teardown
    # Clean up to avoid foreign key constraint issues
    BadgeAssignment.delete_all
    BadgeType.delete_all
  end

  # Association tests
  test 'should have badge associations' do
    assert_respond_to @user, :badge_assignments
    assert_respond_to @user, :badge_types
    assert_respond_to @user, :active_badge_assignments
    assert_respond_to @user, :active_badge_types
  end

  test 'badge associations should work correctly' do
    assert_equal 3, @user.badge_assignments.count
    assert_equal 3, @user.badge_types.count
    assert_equal 2, @user.active_badge_assignments.count
    assert_equal 2, @user.active_badge_types.count
  end

  # Instance method tests
  test 'active_badges should return active badge types ordered by priority' do
    active_badges = @user.active_badges
    assert_equal 2, active_badges.count
    assert_includes active_badges, @badge_type_premium
    assert_includes active_badges, @badge_type_verified
    assert_not_includes active_badges, @badge_type_expert

    # Should be ordered by priority
    assert_equal @badge_type_premium, active_badges.first
  end

  test 'expired_badges should return expired badge types' do
    expired_badges = @user.expired_badges
    assert_equal 1, expired_badges.count
    assert_includes expired_badges, @badge_type_expert
  end

  test 'permanent_badges should return badges without expiration' do
    permanent_badges = @user.permanent_badges

    # The active assignment also has no expires_at (defaults to nil)
    assert_equal 2, permanent_badges.count
    assert_includes permanent_badges, @badge_type_verified
    assert_includes permanent_badges, @badge_type_premium
  end

  test 'has_badge? should work with BadgeType objects' do
    assert @user.has_badge?(@badge_type_premium)
    assert @user.has_badge?(@badge_type_verified)
    assert_not @user.has_badge?(@badge_type_expert) # expired
  end

  test 'has_badge? should work with badge names as strings' do
    assert @user.has_badge?('User Test Premium Writer')
    assert @user.has_badge?('User Test Verified')
    assert_not @user.has_badge?('User Test Expert') # expired
  end

  test 'has_badge? should work with badge names as symbols' do
    assert @user.has_badge?(:'User Test Premium Writer')
    assert @user.has_badge?(:'User Test Verified')
    assert_not @user.has_badge?(:'User Test Expert') # expired
  end

  test 'has_badge? should return false for invalid input' do
    assert_not @user.has_badge?(nil)
    assert_not @user.has_badge?(123)
    assert_not @user.has_badge?([])
  end

  test 'has_any_badge? should work correctly' do
    assert @user.has_any_badge?(@badge_type_premium, @badge_type_expert)
    assert @user.has_any_badge?('User Test Premium Writer', 'Nonexistent')
    assert_not @user.has_any_badge?('Nonexistent', 'Another Nonexistent')
  end

  test 'has_all_badges? should work correctly' do
    assert @user.has_all_badges?(@badge_type_premium, @badge_type_verified)
    assert_not @user.has_all_badges?(@badge_type_premium, @badge_type_expert) # expert is expired
    assert_not @user.has_all_badges?('User Test Premium Writer', 'Nonexistent')
  end

  test 'badge_assignment_for should return correct assignment' do
    assignment = @user.badge_assignment_for(@badge_type_premium)
    assert_equal @active_assignment, assignment

    assignment = @user.badge_assignment_for('User Test Premium Writer')
    assert_equal @active_assignment, assignment

    assignment = @user.badge_assignment_for('Nonexistent')
    assert_nil assignment
  end

  test 'active_badge_assignment_for should return only active assignments' do
    assignment = @user.active_badge_assignment_for(@badge_type_premium)
    assert_equal @active_assignment, assignment

    assignment = @user.active_badge_assignment_for(@badge_type_expert)
    assert_nil assignment # expired

    assignment = @user.active_badge_assignment_for('Nonexistent')
    assert_nil assignment
  end

  test 'badge_count should return count of active badges' do
    assert_equal 2, @user.badge_count
  end

  test 'total_badge_count should return count of all badge assignments' do
    assert_equal 3, @user.total_badge_count
  end

  test 'badges_assigned_by should return assignments by specific admin' do
    assignments = @user.badges_assigned_by(@admin)
    assert_equal 3, assignments.count

    other_admin = users(:admin)
    assignments = @user.badges_assigned_by(other_admin)
    assert_equal 0, assignments.count
  end

  test 'recent_badge_assignments should return recent assignments' do
    recent = @user.recent_badge_assignments(2)
    assert_equal 2, recent.count

    # Should be ordered by assigned_at desc
    assert_equal @active_assignment, recent.first
  end

  # Scope tests
  test 'with_badge scope should work with BadgeType objects' do
    users_with_premium = User.with_badge(@badge_type_premium)
    assert_includes users_with_premium, @user
    assert_not_includes users_with_premium, @other_user

    users_with_expert = User.with_badge(@badge_type_expert)
    assert_not_includes users_with_expert, @user # expired
  end

  test 'with_badge scope should work with badge names' do
    users_with_premium = User.with_badge('User Test Premium Writer')
    assert_includes users_with_premium, @user
    assert_not_includes users_with_premium, @other_user

    users_with_verified = User.with_badge(:'User Test Verified')
    assert_includes users_with_verified, @user
  end

  test 'with_any_badge scope should work correctly' do
    users = User.with_any_badge(@badge_type_premium, @badge_type_expert)
    assert_includes users, @user

    users = User.with_any_badge('Nonexistent Badge')
    assert_not_includes users, @user
  end

  test 'with_all_badges scope should work correctly' do
    users = User.with_all_badges(@badge_type_premium, @badge_type_verified)
    assert_includes users, @user

    users = User.with_all_badges(@badge_type_premium, @badge_type_expert)
    assert_not_includes users, @user # expert is expired
  end

  test 'without_badge scope should work correctly' do
    users = User.without_badge(@badge_type_premium)
    assert_not_includes users, @user
    assert_includes users, @other_user

    users = User.without_badge(@badge_type_expert)
    assert_includes users, @user # expired, so user doesn't have it
  end

  test 'with_badge_count scope should work correctly' do
    users = User.with_badge_count(2)
    assert_includes users, @user

    users = User.with_badge_count(1)
    assert_not_includes users, @user
  end

  test 'with_badge_count_greater_than scope should work correctly' do
    users = User.with_badge_count_greater_than(1)
    assert_includes users, @user

    users = User.with_badge_count_greater_than(2)
    assert_not_includes users, @user
  end

  test 'with_badge_count_less_than scope should work correctly' do
    users = User.with_badge_count_less_than(3)
    assert_includes users, @user
    assert_includes users, @other_user # has 0 badges

    users = User.with_badge_count_less_than(1)
    assert_not_includes users, @user
    assert_includes users, @other_user # has 0 badges
  end

  test 'with_badges_assigned_by scope should work correctly' do
    users = User.with_badges_assigned_by(@admin)
    assert_includes users, @user

    other_admin = users(:admin)
    users = User.with_badges_assigned_by(other_admin)
    assert_not_includes users, @user
  end

  test 'with_recent_badge_assignments scope should work correctly' do
    users = User.with_recent_badge_assignments(7)
    assert_includes users, @user

    users = User.with_recent_badge_assignments(0)
    assert_not_includes users, @user
  end

  # Edge case tests
  test 'scopes should handle empty parameters gracefully' do
    assert_equal User.none, User.with_any_badge
    assert_equal User.all, User.with_all_badges
    assert_equal User.none, User.with_badge(nil)
    assert_equal User.all, User.without_badge(nil)
  end

  test 'user without any badges should work correctly with all methods' do
    assert_equal 0, @other_user.badge_count
    assert_equal 0, @other_user.total_badge_count
    assert_empty @other_user.active_badges
    assert_empty @other_user.expired_badges
    assert_empty @other_user.permanent_badges
    assert_not @other_user.has_badge?(@badge_type_premium)
    assert_not @other_user.has_any_badge?(@badge_type_premium)
    assert_not @other_user.has_all_badges?(@badge_type_premium)
  end

  test 'destroying user should destroy badge assignments' do
    user_id = @user.id
    assignment_ids = @user.badge_assignments.pluck(:id)

    @user.destroy

    assignment_ids.each do |assignment_id|
      assert_not BadgeAssignment.exists?(assignment_id)
    end
  end

  # Onboarding completion tests
  test 'complete_onboarding! should set all onboarding fields' do
    user = users(:one)
    user.update!(
      onboarding_completed: false,
      onboarding_completed_at: nil,
      onboarding_step: 'organization',
    )

    freeze_time do
      user.complete_onboarding!

      user.reload
      assert user.onboarding_completed?
      assert_equal Time.current, user.onboarding_completed_at
      assert_equal 'completed', user.onboarding_step
    end
  end

  test 'onboarding_completed_recently? should return true for recent completion' do
    user = users(:one)
    user.update!(onboarding_completed_at: 1.hour.ago)

    assert user.onboarding_completed_recently?
    assert user.onboarding_completed_recently?(within: 2.hours)
  end

  test 'onboarding_completed_recently? should return false for old completion' do
    user = users(:one)
    user.update!(onboarding_completed_at: 2.days.ago)

    assert_not user.onboarding_completed_recently?
    assert_not user.onboarding_completed_recently?(within: 1.day)
  end

  test 'onboarding_completed_recently? should return false when onboarding_completed_at is nil' do
    user = users(:one)
    user.update!(onboarding_completed_at: nil)

    assert_not user.onboarding_completed_recently?
  end

  test 'onboarding_duration should return correct duration' do
    user = users(:one)
    created_time = 2.days.ago
    completed_time = 1.day.ago

    user.update!(
      created_at: created_time,
      onboarding_completed_at: completed_time,
    )

    expected_duration = completed_time - created_time
    assert_equal expected_duration, user.onboarding_duration
  end

  test 'onboarding_duration should return nil when onboarding_completed_at is nil' do
    user = users(:one)
    user.update!(onboarding_completed_at: nil)

    assert_nil user.onboarding_duration
  end

  test 'onboarding_duration should handle same-day completion' do
    user = users(:one)
    freeze_time do
      created_time = Time.current
      completed_time = Time.current + 2.hours

      user.update!(
        created_at: created_time,
        onboarding_completed_at: completed_time,
      )

      assert_equal 2.hours, user.onboarding_duration
    end
  end
end
