require "test_helper"

class OrganizationTest < ActiveSupport::Test<PERSON>ase
  def setup
    @organization = Organization.new(
      name: "Test Organization",
      email: "<EMAIL>",
      operating_timezone: "America/New_York",
      size: "medium"
    )
  end

  # Basic validation tests
  test "should be valid with valid attributes" do
    assert @organization.valid?
  end

  test "should require name" do
    @organization.name = nil
    assert_not @organization.valid?
    assert_includes @organization.errors[:name], "can't be blank"
  end

  # Industry type field tests
  test "should allow industry_type to be nil" do
    @organization.industry_type = nil
    assert @organization.valid?
  end

  test "should allow industry_type to be blank" do
    @organization.industry_type = ""
    assert @organization.valid?
  end

  test "should allow valid industry_type" do
    @organization.industry_type = "Technology"
    assert @organization.valid?
  end

  test "should reject industry_type longer than 100 characters" do
    @organization.industry_type = "a" * 101
    assert_not @organization.valid?
    assert_includes @organization.errors[:industry_type], "is too long (maximum is 100 characters)"
  end

  test "should allow industry_type exactly 100 characters" do
    @organization.industry_type = "a" * 100
    assert @organization.valid?
  end

  # Onboarding context validation tests
  test "should not require email by default" do
    @organization.email = nil
    assert @organization.valid?
  end

  test "should require email when onboarding_context is true" do
    @organization.onboarding_context = true
    @organization.email = nil
    assert_not @organization.valid?
    assert_includes @organization.errors[:email], "can't be blank"
  end

  test "should require email when onboarding_context is true and email is blank" do
    @organization.onboarding_context = true
    @organization.email = ""
    assert_not @organization.valid?
    assert_includes @organization.errors[:email], "can't be blank"
  end

  test "should not require operating_timezone by default" do
    @organization.operating_timezone = nil
    assert @organization.valid?
  end

  test "should require operating_timezone when onboarding_context is true" do
    @organization.onboarding_context = true
    @organization.operating_timezone = nil
    assert_not @organization.valid?
    assert_includes @organization.errors[:operating_timezone], "can't be blank"
  end

  test "should not require size by default" do
    @organization.size = nil
    assert @organization.valid?
  end

  test "should require size when onboarding_context is true" do
    @organization.onboarding_context = true
    @organization.size = nil
    assert_not @organization.valid?
    assert_includes @organization.errors[:size], "can't be blank"
  end

  test "should validate all onboarding fields when onboarding_context is true" do
    @organization.onboarding_context = true
    @organization.email = nil
    @organization.operating_timezone = nil
    @organization.size = nil
    
    assert_not @organization.valid?
    assert_includes @organization.errors[:email], "can't be blank"
    assert_includes @organization.errors[:operating_timezone], "can't be blank"
    assert_includes @organization.errors[:size], "can't be blank"
  end

  test "should be valid with all onboarding fields when onboarding_context is true" do
    @organization.onboarding_context = true
    assert @organization.valid?
  end

  # Onboarding context helper method tests
  test "onboarding_context? should return false by default" do
    assert_not @organization.send(:onboarding_context?)
  end

  test "onboarding_context? should return true when onboarding_context is set to true" do
    @organization.onboarding_context = true
    assert @organization.send(:onboarding_context?)
  end

  test "onboarding_context? should return false when onboarding_context is set to false" do
    @organization.onboarding_context = false
    assert_not @organization.send(:onboarding_context?)
  end

  test "onboarding_context? should return false when onboarding_context is set to nil" do
    @organization.onboarding_context = nil
    assert_not @organization.send(:onboarding_context?)
  end

  # Integration tests with existing validations
  test "should still validate existing fields" do
    @organization.name = "a" * 101  # Assuming there's a length validation
    @organization.email = "invalid-email"
    
    assert_not @organization.valid?
    # These assertions depend on existing validations in the model
    # Adjust based on actual validation rules
  end

  # Test that onboarding context doesn't interfere with normal operations
  test "should save successfully without onboarding context" do
    assert @organization.save
    assert_not @organization.onboarding_context
  end

  test "should save successfully with onboarding context and valid data" do
    @organization.onboarding_context = true
    assert @organization.save
  end

  # Test industry_type field persistence
  test "should persist industry_type field" do
    @organization.industry_type = "Healthcare"
    @organization.save!
    
    reloaded_org = Organization.find(@organization.id)
    assert_equal "Healthcare", reloaded_org.industry_type
  end

  test "should persist nil industry_type field" do
    @organization.industry_type = nil
    @organization.save!
    
    reloaded_org = Organization.find(@organization.id)
    assert_nil reloaded_org.industry_type
  end

  # Test edge cases
  test "should handle special characters in industry_type" do
    @organization.industry_type = "Technology & Software"
    assert @organization.valid?
  end

  test "should handle unicode characters in industry_type" do
    @organization.industry_type = "Tëchnölögy"
    assert @organization.valid?
  end

  # Test that onboarding_context is not persisted
  test "onboarding_context should not be persisted to database" do
    @organization.onboarding_context = true
    @organization.save!
    
    reloaded_org = Organization.find(@organization.id)
    assert_not reloaded_org.onboarding_context
  end
end
