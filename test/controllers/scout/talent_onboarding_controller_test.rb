require 'test_helper'

class Scout::TalentOnboardingControllerTest < ActionDispatch::IntegrationTest
  setup do
    # Create organization
    @organization = Organization.create!(name: 'Test Organization')

    # Find or create roles
    @scout_role = Role.find_or_create_by!(name: 'scout')
    @talent_role = Role.find_or_create_by!(name: 'talent')

    # Create scout user who has completed scout onboarding
    @scout_user =
      User.create!(
        email: "scout_#{SecureRandom.hex(8)}@example.com",
        password: 'Secret1*3*5*',
        password_confirmation: 'Secret1*3*5*',
        first_name: 'Test',
        last_name: 'Scout',
        time_zone: 'America/New_York',
        verified: true,
        signup_intent: 'scout',
        scout_signup_completed: true,
        talent_signup_completed: false,
        onboarding_step: 'completed',
        onboarding_completed: true,
      )

    # Assign scout role
    UserRole.create!(user: @scout_user, role: @scout_role)

    # Create organization membership
    OrganizationMembership.create!(
      user: @scout_user,
      organization: @organization,
      org_role: 'member',
    )
    @scout_user.update!(last_logged_in_organization_id: @organization.id)

    sign_in_as @scout_user
  end

  test 'should show talent onboarding form for scout user' do
    get scout_new_talent_onboarding_path

    assert_response :success
    assert_select 'h1', text: 'Set up your Talent Profile'
    assert_select 'form[action=?]', scout_create_talent_onboarding_path
  end

  test 'should pre-populate form fields with scout data' do
    get scout_new_talent_onboarding_path

    assert_response :success

    # Check that location is pre-populated based on time zone
    assert_select 'input[name="talent_profile[location]"][value*="New York"]'

    # Check that headline includes user's name
    assert_select 'input[name="talent_profile[headline]"][value*="Test Scout"]'
  end

  test 'should create talent profile and assign role on successful submission' do
    talent_params = {
      talent_profile: {
        headline: 'Expert Content Writer',
        bio: 'I write amazing content',
        location: 'New York, NY',
        skills: 'Writing, Content Strategy, SEO',
        availability_status: 'available',
        pricing_model: 'hourly',
        portfolio_link: 'https://example.com/portfolio',
      },
    }

    assert_difference 'TalentProfile.count', 1 do
      assert_difference 'UserRole.count', 1 do
        post scout_create_talent_onboarding_path, params: talent_params
      end
    end

    @scout_user.reload
    assert @scout_user.talent_signup_completed?
    assert @scout_user.has_role?(:talent)
    assert @scout_user.has_role?(:scout) # Should still have scout role

    assert_redirected_to launchpad_path
    assert_equal 'Welcome to the Talent area! You now have access to both Scout and Talent features. 🎉',
                 flash[:notice]
  end

  test 'should create talent profile even with minimal data' do
    talent_params = { talent_profile: { headline: '', bio: '', location: '' } }

    assert_difference 'TalentProfile.count', 1 do
      post scout_create_talent_onboarding_path, params: talent_params
    end

    assert_redirected_to launchpad_path
    @scout_user.reload
    assert @scout_user.talent_signup_completed?
  end

  test 'should redirect if scout signup not completed' do
    # Create user without scout signup completed
    incomplete_user =
      User.create!(
        email: "incomplete_#{SecureRandom.hex(8)}@example.com",
        password: 'Secret1*3*5*',
        password_confirmation: 'Secret1*3*5*',
        first_name: 'Incomplete',
        last_name: 'User',
        verified: true,
        signup_intent: 'scout',
        scout_signup_completed: false,
        onboarding_step: 'completed',
        onboarding_completed: true,
      )

    OrganizationMembership.create!(
      user: incomplete_user,
      organization: @organization,
      org_role: 'member',
    )
    incomplete_user.update!(last_logged_in_organization_id: @organization.id)

    sign_in_as incomplete_user

    get scout_new_talent_onboarding_path

    assert_redirected_to launchpad_path
    assert_equal 'You need to complete Scout signup to access this area.',
                 flash[:alert]
  end

  test 'should redirect if talent signup already completed' do
    # Mark talent signup as completed
    @scout_user.update!(talent_signup_completed: true)

    get scout_new_talent_onboarding_path

    assert_redirected_to launchpad_path
    assert_equal 'You have already completed Talent onboarding.', flash[:notice]
  end

  test 'should not duplicate talent role if already assigned' do
    # Pre-assign talent role
    UserRole.create!(user: @scout_user, role: @talent_role)

    talent_params = {
      talent_profile: {
        headline: 'Expert Content Writer',
        bio: 'I write amazing content',
        location: 'New York, NY',
        skills: 'Writing, Content Strategy, SEO',
        availability_status: 'available',
        pricing_model: 'hourly',
      },
    }

    assert_difference 'TalentProfile.count', 1 do
      assert_no_difference 'UserRole.count' do
        # Should not create duplicate role
        post scout_create_talent_onboarding_path, params: talent_params
      end
    end

    assert_redirected_to launchpad_path
  end
end
