# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: users
#
#  id                             :bigint           not null, primary key
#  conversations_count            :integer          default(0), not null
#  email                          :string           not null
#  first_name                     :string
#  last_name                      :string
#  onboarding_completed           :boolean          default(FALSE)
#  onboarding_completed_at        :datetime
#  onboarding_step                :string           default("personal")
#  password_digest                :string           not null
#  received_chat_requests_count   :integer          default(0), not null
#  scout_signup_completed         :boolean          default(FALSE)
#  sent_chat_requests_count       :integer          default(0), not null
#  signup_intent                  :string
#  talent_signup_completed        :boolean          default(FALSE)
#  time_zone                      :string
#  verification_email_sent_at     :datetime
#  verified                       :boolean          default(FALSE), not null
#  created_at                     :datetime         not null
#  updated_at                     :datetime         not null
#  last_logged_in_organization_id :integer
#
# Indexes
#
#  index_users_on_email                           (email) UNIQUE
#  index_users_on_first_name                      (first_name)
#  index_users_on_first_name_and_last_name        (first_name,last_name)
#  index_users_on_last_logged_in_organization_id  (last_logged_in_organization_id)
#  index_users_on_last_name                       (last_name)
#  index_users_on_onboarding_completed            (onboarding_completed)
#  index_users_on_onboarding_completed_at         (onboarding_completed_at)
#  index_users_on_scout_signup_completed          (scout_signup_completed)
#  index_users_on_talent_signup_completed         (talent_signup_completed)
#  index_users_on_verified                        (verified)
#  index_users_on_verified_and_created_at         (verified,created_at)
#
lazaro_nixon:
  email: <EMAIL>
  password_digest: <%= BCrypt::Password.create("Secret1*3*5*") %>
  verified: true
  onboarding_completed: true
  onboarding_step: "completed"

one:
  id: <%= ActiveRecord::FixtureSet.identify(:one) %>
  email: <EMAIL>
  password_digest: <%= BCrypt::Password.create("password123123") %>
  verified: true
  first_name: User
  last_name: One
  onboarding_completed: true
  onboarding_step: "completed"

two:
  id: <%= ActiveRecord::FixtureSet.identify(:two) %>
  email: <EMAIL>
  password_digest: <%= BCrypt::Password.create("password123123") %>
  verified: true
  first_name: User
  last_name: Two
  onboarding_completed: true
  onboarding_step: "completed"

admin:
  id: <%= ActiveRecord::FixtureSet.identify(:admin) %>
  email: <EMAIL>
  password_digest: <%= BCrypt::Password.create("Secret1*3*5*") %>
  verified: true
  first_name: Admin
  last_name: User
  onboarding_completed: true
  onboarding_step: "completed"

super_admin:
  id: <%= ActiveRecord::FixtureSet.identify(:super_admin) %>
  email: <EMAIL>
  password_digest: <%= BCrypt::Password.create("Secret1*3*5*") %>
  verified: true
  first_name: Super
  last_name: Admin
  onboarding_completed: true
  onboarding_step: "completed"

scout:
  id: <%= ActiveRecord::FixtureSet.identify(:scout) %>
  email: <EMAIL>
  password_digest: <%= BCrypt::Password.create("Secret1*3*5*") %>
  verified: true
  first_name: Scout
  last_name: User
  onboarding_completed: true
  onboarding_step: "completed"
  scout_signup_completed: true

talent:
  id: <%= ActiveRecord::FixtureSet.identify(:talent) %>
  email: <EMAIL>
  password_digest: <%= BCrypt::Password.create("Secret1*3*5*") %>
  verified: true
  first_name: Talent
  last_name: User
  onboarding_completed: false
  onboarding_step: "personal"
  talent_signup_completed: true
